<?php

namespace FamilyCalendar;

/**
 * NetatmoShortcode class
 *
 * Responsible for rendering weather data from Netatmo API
 */
class NetatmoShortcode
{
    private $date_converter;
    private $client_id;
    private $client_secret;
    private $refresh_token;
    private $access_token;
    private $token_expiry;
    private $api_endpoint = 'https://api.netatmo.com/api/';

    /**
     * Constructor
     *
     * @param DateConvert $date_converter Date converter instance
     */
    public function __construct($date_converter)
    {
        $this->date_converter = $date_converter;

        // Get API credentials from WordPress options
        $this->client_id = get_option('mb_netatmo_client_id', '');
        $this->client_secret = get_option('mb_netatmo_client_secret', '');
        $this->refresh_token = get_option('mb_netatmo_refresh_token', '');
        $this->access_token = get_option('mb_netatmo_access_token', '');
        $this->token_expiry = get_option('mb_netatmo_token_expiry', 0);
    }

    /**
     * Render Netatmo weather data
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render($atts = [])
    {
        // Enqueue styles and scripts
        wp_enqueue_style('mb-netatmo-style');
        wp_enqueue_script('mb-netatmo-script');

        // Default attributes
        $atts = shortcode_atts(
            array(
                'device_id' => '',
                'module_id' => '',
                'type' => 'station', // station, outdoor, indoor
            ),
            $atts,
            'mb_netatmo'
        );

        // Start output buffer
        ob_start();

        // Check if credentials are set
        if (empty($this->client_id) || empty($this->client_secret) || empty($this->refresh_token)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Błąd: Brak konfiguracji API Netatmo. Skonfiguruj ustawienia w panelu administracyjnym.</p>';
            echo '</div>';
            return ob_get_clean();
        }

        // Ensure we have a valid access token
        if (!$this->ensure_valid_token()) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Błąd: Nie można autoryzować z API Netatmo.</p>';
            echo '</div>';
            return ob_get_clean();
        }

        // Get weather data from Netatmo API
        $weather_data = $this->get_weather_data($atts);

        if (is_wp_error($weather_data)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Błąd pobierania danych z Netatmo: ' . esc_html($weather_data->get_error_message()) . '</p>';
            echo '</div>';
        } else {
            // Render the weather data
            $this->render_netatmo_data($weather_data, $atts);
        }

        // Return the output buffer content
        return ob_get_clean();
    }

    /**
     * Ensure we have a valid access token
     *
     * @return bool True if token is valid, false otherwise
     */
    private function ensure_valid_token()
    {
        // Check if token is expired or missing
        if (empty($this->access_token) || time() >= $this->token_expiry) {
            return $this->refresh_access_token();
        }

        return true;
    }

    /**
     * Refresh the access token using the refresh token
     *
     * @return bool True if successful, false otherwise
     */
    private function refresh_access_token()
    {
        $response = wp_remote_post('https://api.netatmo.com/oauth2/token', [
            'body' => [
                'grant_type' => 'refresh_token',
                'refresh_token' => $this->refresh_token,
                'client_id' => $this->client_id,
                'client_secret' => $this->client_secret,
            ],
        ]);

        if (is_wp_error($response)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Netatmo Token Refresh Error: ' . $response->get_error_message());
            }
            return false;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        // Debug token refresh response
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Netatmo Token Refresh Response: ' . print_r($body, true));
        }

        if (isset($body['access_token'])) {
            $this->access_token = $body['access_token'];
            $this->token_expiry = time() + $body['expires_in'];

            // Save the new tokens to WordPress options
            update_option('mb_netatmo_access_token', $this->access_token);
            update_option('mb_netatmo_token_expiry', $this->token_expiry);

            // If a new refresh token is provided, save it too
            if (isset($body['refresh_token'])) {
                $this->refresh_token = $body['refresh_token'];
                update_option('mb_netatmo_refresh_token', $this->refresh_token);
            }

            return true;
        }

        return false;
    }

    /**
     * Get weather data from Netatmo API
     *
     * @param array $atts Shortcode attributes
     * @return array|WP_Error Weather data or error
     */
    private function get_weather_data($atts)
    {
        $endpoint = $this->api_endpoint . 'getstationsdata';

        $response = wp_remote_get($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->access_token,
            ],
        ]);

        if (is_wp_error($response)) {
            return $response;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        // Debug the response
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Netatmo API Response: ' . print_r($body, true));
        }

        if (!isset($body['body']) || !isset($body['body']['devices'])) {
            // Check if there's an error message in the response
            if (isset($body['error']) && !empty($body['error'])) {
                $error_msg = isset($body['error']['message']) ? $body['error']['message'] : $body['error'];
                return new \WP_Error('api_error', 'Błąd API Netatmo: ' . $error_msg);
            }
            return new \WP_Error('invalid_response', 'Nieprawidłowa odpowiedź z API Netatmo');
        }

        // Filter data based on device_id if provided
        if (!empty($atts['device_id'])) {
            $devices = array_filter($body['body']['devices'], function ($device) use ($atts) {
                return $device['_id'] === $atts['device_id'];
            });

            if (empty($devices)) {
                return new \WP_Error('device_not_found', 'Nie znaleziono urządzenia o podanym ID');
            }

            $body['body']['devices'] = array_values($devices);
        }

        return $body['body'];
    }

    /**
     * Render Netatmo data HTML
     *
     * @param array $data Netatmo data
     * @param array $atts Shortcode attributes
     */
    private function render_netatmo_data($data, $atts)
    {
        // Get the first device or the specified device
        $devices = $data['devices'];

        if (empty($devices)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Nie znaleziono urządzeń Netatmo.</p>';
            echo '</div>';
            return;
        }

        $device = $devices[0];

        // Get the module if specified
        $module = null;
        if (!empty($atts['module_id']) && isset($device['modules'])) {
            foreach ($device['modules'] as $mod) {
                if ($mod['_id'] === $atts['module_id']) {
                    $module = $mod;
                    break;
                }
            }
        }

        // Custom module name mapping
        $module_name_mapping = [
            'Wohn EG' => 'Parter',
            'Buro KG' => 'Biuro',
            'Terrase' => 'Taras'
        ];

        // Determine what to render based on the type attribute
        switch ($atts['type']) {
            case 'outdoor':
                echo 'outdoor - 1';
                if (!empty($atts['module_id']) && $module) {
                    $this->render_outdoor_module($device, $module, $module_name_mapping);
                } else {
                    // Find the first outdoor module
                    $outdoor_module = null;
                    if (isset($device['modules'])) {
                        foreach ($device['modules'] as $mod) {
                            if (
                                $mod['type'] === 'NAModule1' || $mod['type'] === 'NAModule2' ||
                                (isset($mod['module_name']) && $mod['module_name'] === 'Terrase')
                            ) {
                                $outdoor_module = $mod;
                                break;
                            }
                        }
                    }

                    if ($outdoor_module) {
                        echo 'outdoor - 2';
                        $this->render_outdoor_module($device, $outdoor_module, $module_name_mapping);
                    } else {
                        echo '<div class="mb-netatmo-error">';
                        echo '<p>Nie znaleziono modułu zewnętrznego.</p>';
                        echo '</div>';
                    }
                }
                break;

            case 'indoor':
                if (!empty($atts['module_id']) && $module) {
                    echo 'indoor - 1';
                    $this->render_indoor_module($device, $module, $module_name_mapping);
                } else {
                    // Find the first indoor module
                    $indoor_module = null;
                    if (isset($device['modules'])) {
                        foreach ($device['modules'] as $mod) {
                            if (
                                $mod['type'] === 'NAModule4' ||
                                (isset($mod['module_name']) && ($mod['module_name'] === 'Wohn EG' || $mod['module_name'] === 'Buro KG'))
                            ) {
                                $indoor_module = $mod;
                                break;
                            }
                        }
                    }

                    if ($indoor_module) {
                        echo 'indoor - 2';
                        $this->render_indoor_module($device, $indoor_module, $module_name_mapping);
                    } else {
                        echo '<div class="mb-netatmo-error">';
                        echo '<p>Nie znaleziono modułu wewnętrznego.</p>';
                        echo '</div>';
                    }
                }
                break;

            default: // 'station' or any other value
                // Get device data
                $dashboard_data = isset($device['dashboard_data']) ? $device['dashboard_data'] : [];
                $station_name = isset($device['station_name']) ? $device['station_name'] : 'Stacja Netatmo';
                $last_update = isset($dashboard_data['time_utc']) ? $dashboard_data['time_utc'] : 0;

                // Format the last update time
                $last_update_formatted = $this->date_converter->getConvertedDate(
                    new \DateTime('@' . $last_update),
                    'd MMMM, HH:mm'
                );

                // Get min/max temperatures if available
                $min_temp = isset($dashboard_data['min_temp']) ? $dashboard_data['min_temp'] : null;
                $max_temp = isset($dashboard_data['max_temp']) ? $dashboard_data['max_temp'] : null;
                $min_temp_time = isset($dashboard_data['date_min_temp']) ? $dashboard_data['date_min_temp'] : null;
                $max_temp_time = isset($dashboard_data['date_max_temp']) ? $dashboard_data['date_max_temp'] : null;

                // Format min/max times if available
                $min_temp_time_formatted = $min_temp_time ? $this->date_converter->getConvertedDate(
                    new \DateTime('@' . $min_temp_time),
                    'HH:mm'
                ) : '';
                $max_temp_time_formatted = $max_temp_time ? $this->date_converter->getConvertedDate(
                    new \DateTime('@' . $max_temp_time),
                    'HH:mm'
                ) : '';

?>
                <div class="mb-netatmo-container">
                    <div class="mb-netatmo-last-update">
                        Ostatnia aktualizacja: <?php echo esc_html($last_update_formatted); ?>
                    </div>

                    <?php if (!empty($dashboard_data)): ?>
                        <div class="mb-netatmo-modules">
                            <?php if (isset($dashboard_data['Temperature'])): ?>
                                <div class="mb-netatmo-temp-container">
                                    <div class="mb-netatmo-temp">
                                        <div class="mb-netatmo-module-title">Pokój dzienny</div>
                                        <span class="mb-netatmo-value"><?php echo esc_html(round($dashboard_data['Temperature'], 1)); ?></span>
                                        <span class="mb-netatmo-unit">&deg;C</span>
                                    </div>

                                    <?php if ($min_temp !== null && $max_temp !== null): ?>
                                        <div class="mb-netatmo-temp-minmax">
                                            <div class="mb-netatmo-temp-min">
                                                <span class="mb-netatmo-minmax-label">Min:</span>
                                                <span class="mb-netatmo-minmax-value"><?php echo esc_html(round($min_temp, 1)); ?>&deg;</span>
                                                <div class="mb-netatmo-minmax-time"><?php echo esc_html($min_temp_time_formatted); ?></div>
                                            </div>
                                            <div class="mb-netatmo-temp-max">
                                                <span class="mb-netatmo-minmax-label">Max:</span>
                                                <span class="mb-netatmo-minmax-value"><?php echo esc_html(round($max_temp, 1)); ?>&deg;</span>
                                                <div class="mb-netatmo-minmax-time"><?php echo esc_html($max_temp_time_formatted); ?></div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <div class="mb-netatmo-details">
                                <?php if (isset($dashboard_data['Humidity'])): ?>
                                    <div class="mb-netatmo-detail">
                                        <span class="mb-netatmo-label">Wilgotność:</span>
                                        <span class="mb-netatmo-value"><?php echo esc_html($dashboard_data['Humidity']); ?>%</span>
                                    </div>
                                <?php endif; ?>

                                <?php if (isset($dashboard_data['CO2'])): ?>
                                    <div class="mb-netatmo-detail">
                                        <span class="mb-netatmo-label">CO2:</span>
                                        <span class="mb-netatmo-value"><?php echo esc_html($dashboard_data['CO2']); ?> ppm</span>
                                    </div>
                                <?php endif; ?>

                                <?php if (isset($dashboard_data['Pressure'])): ?>
                                    <div class="mb-netatmo-detail">
                                        <span class="mb-netatmo-label">Ciśnienie:</span>
                                        <span class="mb-netatmo-value"><?php echo esc_html($dashboard_data['Pressure']); ?> mbar</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="mb-netatmo-error">
                            <p>Brak danych pomiarowych dla stacji głównej.</p>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($device['modules']) && !empty($device['modules'])): ?>
                        <div class="mb-netatmo-modules">
                            <?php foreach ($device['modules'] as $module): ?>
                                <?php
                                // Get custom module name if available
                                $original_name = isset($module['module_name']) ? $module['module_name'] : 'Moduł';
                                $display_name = isset($module_name_mapping[$original_name]) ? $module_name_mapping[$original_name] : $original_name;

                                $this->render_custom_indoor_module($module, $display_name);
                                // $this->render_custom_outdoor_module($module, $display_name);

                                ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php
                break;
        }
    }

    /**
     * Render outdoor module data
     *
     * @param array $device Main device data
     * @param array $module Module data
     * @param array $module_name_mapping Custom module name mapping
     */
    private function render_outdoor_module($device, $module, $module_name_mapping = [])
    {
        if (empty($module)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Nie znaleziono modułu zewnętrznego o podanym ID.</p>';
            echo '</div>';
            return;
        }

        // Get custom module name if available
        $original_name = isset($module['module_name']) ? $module['module_name'] : 'Moduł zewnętrzny';
        $display_name = isset($module_name_mapping[$original_name]) ? $module_name_mapping[$original_name] : $original_name;

        if ($original_name === 'Terrase') {
            // $this->render_custom_outdoor_module($module, $display_name);
        } else {
            $dashboard_data = isset($module['dashboard_data']) ? $module['dashboard_data'] : [];
            $last_update = isset($dashboard_data['time_utc']) ? $dashboard_data['time_utc'] : 0;

            // Format the last update time
            $last_update_formatted = $this->date_converter->getConvertedDate(
                new \DateTime('@' . $last_update),
                'd MMMM, HH:mm'
            );

            // Get min/max temperatures if available
            $min_temp = isset($dashboard_data['min_temp']) ? $dashboard_data['min_temp'] : null;
            $max_temp = isset($dashboard_data['max_temp']) ? $dashboard_data['max_temp'] : null;
            $min_temp_time = isset($dashboard_data['date_min_temp']) ? $dashboard_data['date_min_temp'] : null;
            $max_temp_time = isset($dashboard_data['date_max_temp']) ? $dashboard_data['date_max_temp'] : null;

            // Format min/max times if available
            $min_temp_time_formatted = $min_temp_time ? $this->date_converter->getConvertedDate(
                new \DateTime('@' . $min_temp_time),
                'HH:mm'
            ) : '';
            $max_temp_time_formatted = $max_temp_time ? $this->date_converter->getConvertedDate(
                new \DateTime('@' . $max_temp_time),
                'HH:mm'
            ) : '';

            ?>
            <div class="mb-netatmo-container mb-netatmo-outdoor">
                <div class="mb-netatmo-last-update">
                    Ostatnia aktualizacja: <?php echo esc_html($last_update_formatted); ?>
                </div>
                
                <?php if (!empty($dashboard_data)): ?>
                    <div class="mb-netatmo-main">
                        <?php if (isset($dashboard_data['Temperature'])): ?>
                            <div class="mb-netatmo-temp-container">
                                <div class="mb-netatmo-temp">
                                    <h3 class="mb-netatmo-module-title"><?php echo esc_html($display_name); ?></h3>
                                    <span class="mb-netatmo-value"><?php echo esc_html(round($dashboard_data['Temperature'], 1)); ?></span>
                                    <span class="mb-netatmo-unit">&deg;C</span>
                                </div>

                                <?php if ($min_temp !== null && $max_temp !== null): ?>
                                    <div class="mb-netatmo-temp-minmax">
                                        <div class="mb-netatmo-temp-min">
                                            <span class="mb-netatmo-minmax-label">Min:</span>
                                            <span class="mb-netatmo-minmax-value"><?php echo esc_html(round($min_temp, 1)); ?>&deg;</span>
                                            <div class="mb-netatmo-minmax-time"><?php echo esc_html($min_temp_time_formatted); ?></div>
                                        </div>
                                        <div class="mb-netatmo-temp-max">
                                            <span class="mb-netatmo-minmax-label">Max:</span>
                                            <span class="mb-netatmo-minmax-value"><?php echo esc_html(round($max_temp, 1)); ?>&deg;</span>
                                            <div class="mb-netatmo-minmax-time"><?php echo esc_html($max_temp_time_formatted); ?></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="mb-netatmo-details">
                            <?php if (isset($dashboard_data['Humidity'])): ?>
                                <div class="mb-netatmo-detail">
                                    <span class="mb-netatmo-label">Wilgotność:</span>
                                    <span class="mb-netatmo-value"><?php echo esc_html($dashboard_data['Humidity']); ?>%</span>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($dashboard_data['WindStrength'])): ?>
                                <div class="mb-netatmo-detail">
                                    <span class="mb-netatmo-label">Wiatr:</span>
                                    <span class="mb-netatmo-value"><?php echo esc_html($dashboard_data['WindStrength']); ?> km/h</span>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($dashboard_data['Rain'])): ?>
                                <div class="mb-netatmo-detail">
                                    <span class="mb-netatmo-label">Deszcz:</span>
                                    <span class="mb-netatmo-value"><?php echo esc_html($dashboard_data['Rain']); ?> mm</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="mb-netatmo-error">
                        <p>Brak danych pomiarowych dla modułu zewnętrznego.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php
        }
    }

    /**
     * Render indoor module data
     *
     * @param array $device Main device data
     * @param array $module Module data
     * @param array $module_name_mapping Custom module name mapping
     */
    private function render_indoor_module($device, $module, $module_name_mapping = [])
    {
        if (empty($module)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Nie znaleziono modułu wewnętrznego o podanym ID.</p>';
            echo '</div>';
            return;
        }
    }

    /**
     * Render custom indoor module (Wohn KG, Büro KG)
     * 
     * @param array $module Module data
     * @param string $display_name Display name for the module
     */
    private function render_custom_indoor_module($module, $display_name)
    {
        // Convert module name to CSS-friendly class name
        $class_name = 'mb-netatmo-module-' . strtolower(str_replace(' ', '-', $display_name));

        // Get dashboard data
        $dashboard_data = isset($module['dashboard_data']) ? $module['dashboard_data'] : [];
        if (empty($dashboard_data)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Brak danych pomiarowych dla modułu ' . esc_html($display_name) . '.</p>';
            echo '</div>';
            return;
        }

        // Get last update time
        $last_update = isset($dashboard_data['time_utc']) ? $dashboard_data['time_utc'] : 0;
        $last_update_formatted = $this->date_converter->getConvertedDate(
            new \DateTime('@' . $last_update),
            'd MMMM, HH:mm'
        );

        // Get min/max temperatures if available
        $min_temp = isset($dashboard_data['min_temp']) ? $dashboard_data['min_temp'] : null;
        $max_temp = isset($dashboard_data['max_temp']) ? $dashboard_data['max_temp'] : null;
        $min_temp_time = isset($dashboard_data['date_min_temp']) ? $dashboard_data['date_min_temp'] : null;
        $max_temp_time = isset($dashboard_data['date_max_temp']) ? $dashboard_data['date_max_temp'] : null;

        // Format min/max times if available
        $min_temp_time_formatted = $min_temp_time ? $this->date_converter->getConvertedDate(
            new \DateTime('@' . $min_temp_time),
            'HH:mm'
        ) : '';
        $max_temp_time_formatted = $max_temp_time ? $this->date_converter->getConvertedDate(
            new \DateTime('@' . $max_temp_time),
            'HH:mm'
        ) : '';

        // custom display name
        $custom_display_name = $display_name === 'Tarasse' ? "taras" : 'biuro';

        ?>
        <div class="mb-netatmo-custom-indoor <?php echo esc_attr($class_name); ?>">
            
            <?php if (isset($dashboard_data['Temperature'])): ?>
                <div class="mb-netatmo-temp-container">
                    <div class="mb-netatmo-temp">
                        <div class="mb-netatmo-module-title"><?php echo esc_html($custom_display_name); ?></div>
                        <span class="mb-netatmo-value"><?php echo esc_html(round($dashboard_data['Temperature'], 1)); ?></span>
                        <span class="mb-netatmo-unit">&deg;C</span>
                    </div>
                    <?php if ($min_temp !== null && $max_temp !== null): ?>
                        <div class="mb-netatmo-temp-minmax">
                            <div class="mb-netatmo-temp-min">
                                <span class="mb-netatmo-minmax-label">Min:</span>
                                <span class="mb-netatmo-minmax-value"><?php echo esc_html(round($min_temp, 1)); ?>&deg;</span>
                                <div class="mb-netatmo-minmax-time"><?php echo esc_html($min_temp_time_formatted); ?></div>
                            </div>
                            <div class="mb-netatmo-temp-max">
                                <span class="mb-netatmo-minmax-label">Max:</span>
                                <span class="mb-netatmo-minmax-value"><?php echo esc_html(round($max_temp, 1)); ?>&deg;</span>
                                <div class="mb-netatmo-minmax-time"><?php echo esc_html($max_temp_time_formatted); ?></div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="mb-netatmo-details">
                <?php if (isset($dashboard_data['Humidity'])): ?>
                    <div class="mb-netatmo-detail">
                        <span class="mb-netatmo-label">Wilgotność:</span>
                        <span class="mb-netatmo-value"><?php echo esc_html($dashboard_data['Humidity']); ?>%</span>
                    </div>
                <?php endif; ?>

                <?php if (isset($dashboard_data['CO2'])): ?>
                    <?php $co2_class = $this->get_co2_class($dashboard_data['CO2']); ?>
                    <div class="mb-netatmo-module-detail co2-<?php echo esc_attr($co2_class); ?>">
                        <div class="mb-netatmo-module-label">CO2</div>
                        <div class="mb-netatmo-module-value"><?php echo esc_html($dashboard_data['CO2']); ?> ppm</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php
    }

    /**
     * Render custom outdoor module (Terrase)
     * 
     * @param array $module Module data
     * @param string $display_name Display name for the module
     */
    private function render_custom_outdoor_module($module, $display_name)
    {
        echo $display_name . 'render_custom_outdoor_module';
        // Convert module name to CSS-friendly class name
        $class_name = 'mb-netatmo-module-' . strtolower(str_replace(' ', '-', $display_name));

        // Get dashboard data
        $dashboard_data = isset($module['dashboard_data']) ? $module['dashboard_data'] : [];
        if (empty($dashboard_data)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Brak danych pomiarowych dla modułu ' . esc_html($display_name) . '.</p>';
            echo '</div>';
            return;
        }

        // Get last update time
        $last_update = isset($dashboard_data['time_utc']) ? $dashboard_data['time_utc'] : 0;
        $last_update_formatted = $this->date_converter->getConvertedDate(
            new \DateTime('@' . $last_update),
            'd MMMM, HH:mm'
        );

        // Get min/max temperatures if available
        $min_temp = isset($dashboard_data['min_temp']) ? $dashboard_data['min_temp'] : null;
        $max_temp = isset($dashboard_data['max_temp']) ? $dashboard_data['max_temp'] : null;
        $min_temp_time = isset($dashboard_data['date_min_temp']) ? $dashboard_data['date_min_temp'] : null;
        $max_temp_time = isset($dashboard_data['date_max_temp']) ? $dashboard_data['date_max_temp'] : null;

        // Format min/max times if available
        $min_temp_time_formatted = $min_temp_time ? $this->date_converter->getConvertedDate(
            new \DateTime('@' . $min_temp_time),
            'HH:mm'
        ) : '';
        $max_temp_time_formatted = $max_temp_time ? $this->date_converter->getConvertedDate(
            new \DateTime('@' . $max_temp_time),
            'HH:mm'
        ) : '';
    ?>
        <div class="mb-netatmo-custom-outdoor <?php echo esc_attr($class_name); ?>">
            <div class="mb-netatmo-module-last-update">
                Ostatnia aktualizacja: <?php echo esc_html($last_update_formatted); ?>
            </div>
            
            <?php if (isset($dashboard_data['Temperature'])): ?>
                <div class="mb-netatmo-module-title"><?php echo esc_html($display_name); ?></div>ś
                <div class="mb-netatmo-module-temp-container">
                    <div class="mb-netatmo-temp">
                        <span class="mb-netatmo-value"><?php echo esc_html(round($dashboard_data['Temperature'], 1)); ?></span>
                        <span class="mb-netatmo-unit">&deg;C</span>
                    </div>

                    <?php if ($min_temp !== null && $max_temp !== null): ?>
                        <div class="mb-netatmo-module-temp-minmax">
                            <div class="mb-netatmo-module-temp-min">
                                <span class="mb-netatmo-module-minmax-label">Min:</span>
                                <span class="mb-netatmo-module-minmax-value"><?php echo esc_html(round($min_temp, 1)); ?>&deg;</span>
                                <span class="mb-netatmo-module-minmax-time"><?php echo esc_html($min_temp_time_formatted); ?></span>
                            </div>
                            <div class="mb-netatmo-module-temp-max">
                                <span class="mb-netatmo-module-minmax-label">Max:</span>
                                <span class="mb-netatmo-module-minmax-value"><?php echo esc_html(round($max_temp, 1)); ?>&deg;</span>
                                <span class="mb-netatmo-module-minmax-time"><?php echo esc_html($max_temp_time_formatted); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="mb-netatmo-module-details">
                <?php if (isset($dashboard_data['Humidity'])): ?>
                    <div class="mb-netatmo-module-detail">
                        <div class="mb-netatmo-module-label">Wilgotność</div>
                        <div class="mb-netatmo-module-value"><?php echo esc_html($dashboard_data['Humidity']); ?>%</div>
                    </div>
                <?php endif; ?>

                <?php if (isset($dashboard_data['WindStrength'])): ?>
                    <div class="mb-netatmo-module-detail">
                        <div class="mb-netatmo-module-label">Wiatr</div>
                        <div class="mb-netatmo-module-value"><?php echo esc_html($dashboard_data['WindStrength']); ?> km/h</div>
                    </div>
                <?php endif; ?>

                <?php if (isset($dashboard_data['Rain'])): ?>
                    <div class="mb-netatmo-module-detail">
                        <div class="mb-netatmo-module-label">Deszcz</div>
                        <div class="mb-netatmo-module-value"><?php echo esc_html($dashboard_data['Rain']); ?> mm</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php
    }

    /**
     * Get CO2 class based on value
     * 
     * @param int $co2 CO2 value in ppm
     * @return string Class name (good, warning, danger)
     */
    private function get_co2_class($co2)
    {
        if ($co2 < 800) {
            return 'good';
        } elseif ($co2 < 1200) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    /**
     * Render a summary of a module (for the station view)
     *
     * @param array $module Module data
     */
    private function render_module_summary($module)
    {
        $dashboard_data = isset($module['dashboard_data']) ? $module['dashboard_data'] : [];
        $module_name = isset($module['module_name']) ? $module['module_name'] : 'Moduł';
        $module_type = isset($module['type']) ? $module['type'] : '';

        if (empty($dashboard_data)) {
            return;
        }

    ?>
        <div class="mb-netatmo-module-summary">
            
            <div class="mb-netatmo-module-data">
                <?php if (isset($dashboard_data['Temperature'])): ?>
                    <h5 class="mb-netatmo-module-name"><?php echo esc_html($module_name); ?></h5>
                    <div class="mb-netatmo-temp">
                        <span class="mb-netatmo-value"><?php echo esc_html(round($dashboard_data['Temperature'], 1)); ?></span>
                        <span class="mb-netatmo-unit">&deg;C</span>
                    </div>
                <?php endif; ?>

                <?php if (isset($dashboard_data['Humidity'])): ?>
                    <div class="mb-netatmo-module-humidity">
                        <?php echo esc_html($dashboard_data['Humidity']); ?>%
                    </div>
                <?php endif; ?>

                <?php if ($module_type === 'NAModule2' && isset($dashboard_data['WindStrength'])): ?>
                    <div class="mb-netatmo-module-wind">
                        <?php echo esc_html($dashboard_data['WindStrength']); ?> km/h
                    </div>
                <?php endif; ?>

                <?php if ($module_type === 'NAModule3' && isset($dashboard_data['Rain'])): ?>
                    <div class="mb-netatmo-module-rain">
                        <?php echo esc_html($dashboard_data['Rain']); ?> mm
                    </div>
                <?php endif; ?>
            </div>
        </div>
<?php
    }
}