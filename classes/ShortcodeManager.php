<?php

namespace FamilyCalendar;

/**
 * ShortcodeManager class
 * 
 * Responsible for registering all shortcodes and initializing their handlers
 */
class ShortcodeManager
{
    private $weather_shortcode;
    private $calendar_shortcode;
    private $calendar_all_shortcode;
    private $netatmo_shortcode;
    private $today;
    private $year;

    /**
     * Constructor
     * 
     * @param string $date Optional date parameter
     */
    public function __construct($date = '')
    {
        // Initialize date properties
        $this->today = new \DateTime($date);
        $this->year = $this->today->format('Y');

        // Initialize shortcode handlers
        $this->init_shortcode_handlers();

        // Register shortcodes
        $this->register_shortcodes();

        // Register cron job
        $this->register_cron_job();
    }

    /**
     * Initialize shortcode handler classes
     */
    private function init_shortcode_handlers()
    {
        $date_converter = DateConvert::getInstance();
        
        $this->weather_shortcode = new WeatherShortcode($date_converter);
        $this->calendar_shortcode = new CalendarShortcode($this->today, $this->year, $date_converter);
        $this->calendar_all_shortcode = new CalendarAllShortcode($this->year);
        $this->netatmo_shortcode = new NetatmoShortcode($date_converter);
    }

    /**
     * Register all shortcodes
     */
    private function register_shortcodes()
    {
        add_shortcode('mb_calendar_weather', [$this->weather_shortcode, 'render']);
        add_shortcode('mb_calendar', [$this->calendar_shortcode, 'render']);
        add_shortcode('mb_calendar_all', [$this->calendar_all_shortcode, 'render']);
        add_shortcode('mb_netatmo', [$this->netatmo_shortcode, 'render']);
    }

    /**
     * Register cron job for data updates
     */
    private function register_cron_job()
    {
        // Register cron job action
        add_action('mb_daily_calendar_update', [$this, 'update_calendar_data']);

        // Schedule the cron job if not already scheduled
        if (!wp_next_scheduled('mb_daily_calendar_update')) {
            wp_schedule_event(strtotime('tomorrow midnight'), 'daily', 'mb_daily_calendar_update');
        }
    }

    /**
     * Update calendar data (cron job callback)
     */
    public function update_calendar_data()
    {
        $read_json = new ReadJsonData();
        $data = $read_json->get_json_data($this->year);

        // Save updated data to WordPress options
        update_option('mb_calendar_data', $data);
    }
}
