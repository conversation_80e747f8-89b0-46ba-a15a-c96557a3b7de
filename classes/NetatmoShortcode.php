<?php

namespace FamilyCalendar;

/**
 * NetatmoShortcode class
 *
 * Responsible for rendering weather data from Netatmo API
 */
class NetatmoShortcode
{
    private $date_converter;
    private $client_id;
    private $client_secret;
    private $refresh_token;
    private $access_token;
    private $token_expiry;
    private $api_endpoint = 'https://api.netatmo.com/api/';

    /**
     * Constructor
     *
     * @param DateConvert $date_converter Date converter instance
     */
    public function __construct($date_converter)
    {
        $this->date_converter = $date_converter;

        // Get API credentials from WordPress options
        $this->client_id = get_option('mb_netatmo_client_id', '');
        $this->client_secret = get_option('mb_netatmo_client_secret', '');
        $this->refresh_token = get_option('mb_netatmo_refresh_token', '');
        $this->access_token = get_option('mb_netatmo_access_token', '');
        $this->token_expiry = get_option('mb_netatmo_token_expiry', 0);
    }

    /**
     * Render Netatmo weather data
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render($atts = [])
    {
        // Enqueue styles and scripts
        wp_enqueue_style('mb-netatmo-style');
        wp_enqueue_script('mb-netatmo-script');

        // Default attributes
        $atts = shortcode_atts(
            array(
                'device_id' => '',
                'module_id' => '',
                'type' => 'station',
            ),
            $atts,
            'mb_netatmo'
        );

        // Start output buffer
        ob_start();

        // Check if credentials are set
        if (empty($this->client_id) || empty($this->client_secret) || empty($this->refresh_token)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Błąd: Brak konfiguracji API Netatmo. Skonfiguruj ustawienia w panelu administracyjnym.</p>';
            echo '</div>';
            return ob_get_clean();
        }

        // Ensure we have a valid access token
        if (!$this->ensure_valid_token()) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Błąd: Nie można autoryzować z API Netatmo.</p>';
            echo '</div>';
            return ob_get_clean();
        }

        // Get weather data from Netatmo API
        $weather_data = $this->get_weather_data($atts);

        if (is_wp_error($weather_data)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Błąd pobierania danych z Netatmo: ' . esc_html($weather_data->get_error_message()) . '</p>';
            echo '</div>';
        } else {
            // Render the weather data
            $this->render_netatmo_data($weather_data, $atts);
        }

        // Return the output buffer content
        return ob_get_clean();
    }

    /**
     * Ensure we have a valid access token
     *
     * @return bool True if token is valid, false otherwise
     */
    private function ensure_valid_token()
    {
        // Check if token is expired or missing
        if (empty($this->access_token) || time() >= $this->token_expiry) {
            return $this->refresh_access_token();
        }

        return true;
    }

    /**
     * Refresh the access token using the refresh token
     *
     * @return bool True if successful, false otherwise
     */
    private function refresh_access_token()
    {
        $response = wp_remote_post('https://api.netatmo.com/oauth2/token', [
            'body' => [
                'grant_type' => 'refresh_token',
                'refresh_token' => $this->refresh_token,
                'client_id' => $this->client_id,
                'client_secret' => $this->client_secret,
            ],
        ]);

        if (is_wp_error($response)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Netatmo Token Refresh Error: ' . $response->get_error_message());
            }
            return false;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        // Debug token refresh response
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Netatmo Token Refresh Response: ' . print_r($body, true));
        }

        if (isset($body['access_token'])) {
            $this->access_token = $body['access_token'];
            $this->token_expiry = time() + $body['expires_in'];

            // Save the new tokens to WordPress options
            update_option('mb_netatmo_access_token', $this->access_token);
            update_option('mb_netatmo_token_expiry', $this->token_expiry);

            // If a new refresh token is provided, save it too
            if (isset($body['refresh_token'])) {
                $this->refresh_token = $body['refresh_token'];
                update_option('mb_netatmo_refresh_token', $this->refresh_token);
            }

            return true;
        }

        return false;
    }

    /**
     * Get weather data from Netatmo API
     *
     * @param array $atts Shortcode attributes
     * @return array|WP_Error Weather data or error
     */
    private function get_weather_data($atts)
    {
        $endpoint = $this->api_endpoint . 'getstationsdata';

        $response = wp_remote_get($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->access_token,
            ],
        ]);

        if (is_wp_error($response)) {
            return $response;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        // Debug the response
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Netatmo API Response: ' . print_r($body, true));
        }

        if (!isset($body['body']) || !isset($body['body']['devices'])) {
            // Check if there's an error message in the response
            if (isset($body['error']) && !empty($body['error'])) {
                $error_msg = isset($body['error']['message']) ? $body['error']['message'] : $body['error'];
                return new \WP_Error('api_error', 'Błąd API Netatmo: ' . $error_msg);
            }
            return new \WP_Error('invalid_response', 'Nieprawidłowa odpowiedź z API Netatmo');
        }

        // Filter data based on device_id if provided
        if (!empty($atts['device_id'])) {
            $devices = array_filter($body['body']['devices'], function ($device) use ($atts) {
                return $device['_id'] === $atts['device_id'];
            });

            if (empty($devices)) {
                return new \WP_Error('device_not_found', 'Nie znaleziono urządzenia o podanym ID');
            }

            $body['body']['devices'] = array_values($devices);
        }

        return $body['body'];
    }

    /**
     * Render Netatmo data HTML
     *
     * @param array $data Netatmo data
     * @param array $atts Shortcode attributes
     */
    private function render_netatmo_data($data, $atts)
    {
        $devices = $data['devices'];
        if (empty($devices)) {
            echo '<div class="mb-netatmo-error">';
            echo '<p>Nie znaleziono urządzeń Netatmo.</p>';
            echo '</div>';
            return;
        }

        $device = $devices[0];

        // Get the module if specified
        $module = null;
        if (!empty($atts['module_id']) && isset($device['modules'])) {
            foreach ($device['modules'] as $mod) {
                if ($mod['_id'] === $atts['module_id']) {
                    $module = $mod;
                    break;
                }
            }
        }

        // Custom module name mapping
        $module_mapping = [
            'Terrase' => [
                'name' => 'Taras',
                'data' => $device['modules'][0]['dashboard_data'],
            ],
            'Wohn EG' => [
                'name' =>  'Parter',
                'data' => $device['dashboard_data'],
            ],
            'Buro KG' => [
                'name' => 'Biuro',
                'data' => $device['modules'][1]['dashboard_data'],
            ],
        ];



        // render based on the type attribute
        $last_update = isset($dashboard_data['time_utc']) ? $dashboard_data['time_utc'] : 0;

        // Format the last update time
        $last_update_formatted = $this->date_converter->getConvertedDate(
            new \DateTime('@' . $last_update),
            'd MMMM, HH:mm'
        );

        // Render the station view
        foreach ($module_mapping as $module) {
            $this->render_module($module['data'], $module['name']);
        }
    }

    /**
     * Render module
     */

    private function render_module($data, $display_name)
    {
?>
        <?php if (!empty($data)): ?>
            <section class="mb-netatmo-modules">
                <?php if (isset($data['Temperature'])): ?>
                    <?php
                    $weather_data = $this->get_current_weather_icon_text();
                    $icon = isset($weather_data[0]) ? $weather_data[0] : '';
                    $condition = isset($weather_data[1]) ? $weather_data[1] : '';

                    ?>

                    <section class="mb-netatmo-temp-container">
                        <div class="mb-netatmo-temp">
                            <div class="mb-netatmo-title">
                                <?php echo esc_html($display_name); ?>
                                <?php if ($display_name === 'Taras'): ?>
                                    <img src="<?php echo esc_url($icon); ?>" alt="<?php echo esc_attr($condition); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="mb-netatmo-value-container">
                                <span class="mb-netatmo-value-main">
                                    <?php echo esc_html(str_replace('.', ',', $data['Temperature'])); ?>
                                </span>
                                <span class="mb-netatmo-unit">&deg;C</span>
                            </div>
                            <?php if ($display_name === 'Taras'): ?>
                                <div class="mb-netatmo-condition">
                                    <?php echo esc_html($condition); ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if ($data['min_temp'] !== null && $data['max_temp'] !== null): ?>
                            <div class="mb-netatmo-temp-minmax">
                                <div class="mb-netatmo-temp-min">
                                    <span class="mb-netatmo-minmax-label">min:</span>
                                    <span class="mb-netatmo-minmax-value"><?php echo esc_html(str_replace('.', ',', $data['min_temp'])); ?></span>
                                    <span class="mb-netatmo-unit">&deg;C</span>
                                    <div class="mb-netatmo-minmax-time"><?php echo esc_html($this->convert_time($data['date_min_temp'])); ?></div>
                                </div>
                                <div class="mb-netatmo-temp-max">
                                    <span class="mb-netatmo-minmax-label">max:</span>
                                    <span class="mb-netatmo-minmax-value"><?php echo esc_html(str_replace('.', ',', $data['max_temp'])); ?></span>
                                    <span class="mb-netatmo-unit">&deg;C</span>
                                    <div class="mb-netatmo-minmax-time"><?php echo esc_html($this->convert_time($data['date_max_temp'])); ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </section>
                <?php endif; ?>

                <div class="mb-netatmo-details">
                    <?php if (isset($data['Humidity'])): ?>
                        <div class="mb-netatmo-detail">
                            <span class="mb-netatmo-label">Wilgotność:</span>
                            <span class="mb-netatmo-value"><?php echo esc_html($data['Humidity']); ?>%</span>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($data['CO2'])): ?>
                        <div class="mb-netatmo-detail">
                            <span class="mb-netatmo-label">CO2:</span>
                            <span class="mb-netatmo-value"><?php echo esc_html($data['CO2']); ?> ppm</span>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($data['Pressure'])): ?>
                        <div class="mb-netatmo-detail">
                            <span class="mb-netatmo-label">Ciśnienie:</span>
                            <span class="mb-netatmo-value"><?php echo esc_html(round($data['Pressure'])); ?> mbar</span>
                        </div>
                    <?php endif; ?>
                </div>
            </section>
        <?php else: ?>
            <div class="mb-netatmo-error">
                <p>Brak danych pomiarowych dla stacji głównej.</p>
            </div>
        <?php endif; ?>
<?php
    }

    /**
     * Get CO2 class based on value
     * 
     * @param int $co2 CO2 value in ppm
     * @return string Class name (good, warning, danger)
     */
    private function get_co2_class($co2)
    {
        if ($co2 < 800) {
            return 'good';
        } elseif ($co2 < 1200) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    /**
     * Time converter
     * from timestamp to HH:mm
     */
    private function convert_time($timestamp)
    {
        return $this->date_converter->getConvertedDate(
            new \DateTime('@' . $timestamp),
            'HH:mm'
        );
    }

    /**
     * Get weather icon based on available data
     * 
     * @param array $data Weather data
     * @return string HTML for weather icon
     */
    private function get_weather_icon($data)
    {
        // Default icon (sunny)
        $icon = '<i class="fas fa-sun" style="color: #FFD700; margin-left: 5px;"></i>';

        // Check for rain if available
        if (isset($data['Rain']) && $data['Rain'] > 0) {
            $icon = '<i class="fas fa-cloud-rain" style="color: #4682B4; margin-left: 5px;"></i>';
        }
        // Check for high humidity but no rain (cloudy)
        elseif (isset($data['Humidity']) && $data['Humidity'] > 80) {
            $icon = '<i class="fas fa-cloud" style="color: #708090; margin-left: 5px;"></i>';
        }
        // Check for moderate humidity (partly cloudy)
        elseif (isset($data['Humidity']) && $data['Humidity'] > 60) {
            $icon = '<i class="fas fa-cloud-sun" style="color: #87CEEB; margin-left: 5px;"></i>';
        }

        return $icon;
    }

    /**
     * Get current weather icon
     */
    private function get_current_weather_icon_text()
    {
        $current_api_url = 'http://api.weatherapi.com/v1/current.json?key=bd2e82900e5c40819be64538251405&q=Lebach&lang=pl&aqi=no';
        $current_response = wp_remote_get($current_api_url);

        if (is_wp_error($current_response)) {
            echo '';
        } else {
            $current_data = json_decode(wp_remote_retrieve_body($current_response), true);
        }

        if (
            isset($current_data['location']) && isset($current_data['current'])
        ) {
            $current_condition = $current_data['current']['condition']['text'];
            $current_icon = $current_data['current']['condition']['icon'];

            return [$current_icon, $current_condition];
        } else {
            return '';
        }
    }
}
