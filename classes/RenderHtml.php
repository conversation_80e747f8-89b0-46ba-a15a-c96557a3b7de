<?php

namespace FamilyCalendar;
class RenderHtml {
	private $today, $month, $day, $date_to_render;
	private $date_converter;

	public function __construct( $date = '' ) {

		date_default_timezone_set( 'Europe/Warsaw' );
		$this->date_to_render = $date;
		$this->today          = new \DateTime( $date );
		$this->month          = (int) $this->today->format( 'm' );
		$this->day            = (int) $this->today->format( 'd' );

		$this->date_converter = DateConvert::getInstance();
	}

	/**
	 * rendering a month view
	 * all events in current month
	 * $render_attr: 'year' - all events in the year, 'month' - only monthly events
	 */
	public function render_calendar_events( $data, $date = '', $render_attr = '' ) {

		$prev_month    = '';
		$month_flag    = $render_attr == 'year';
		$date_to_convert = $date ? $date : $this->today;
		$formattedDate = $this->date_converter->getConvertedDate( $date_to_convert, 'MMMM yyyy' );

		echo '<div class="mb-month-calendar">';
		if ( !$month_flag ) {
			echo '<h4 class="mb-month-calendar-title">wydarzenia ' . esc_html( $formattedDate ) . '</h4>';
			echo '<hr>';
		}

		foreach ( $data as $event ) {
			// Validate and create a DateTime object
			try {
				$eventDate = new \DateTime( $event['date'] );
				$formatter = $this->date_converter->getConvertedDate( $eventDate, 'd MMMM' );
			} catch ( \Exception $e ) {
				// Skip invalid dates
				continue;
			}

			$eventMonth   = $eventDate->format( 'm' );
			$eventDay     = $eventDate->format( 'd' );
			$currentMonth = $this->month;
			$currentDay   = $this->day;

			// Skip events not in the current month or events on the current day
			if ( ! $month_flag && ( $eventMonth != $currentMonth || $eventDay == $currentDay ) ) {
				continue;
			}

			// Collect event details
			$eventDetails = [];


			if ( ! empty( $event['nameDay'] ) ) {
				$eventDetails[] = esc_html( $event['nameDay'] );
			}
			if ( ! empty( $event['birthday'] ) ) {
				$eventDetails[] = esc_html( $event['birthday'] );
			}
			if ( ! empty( $event['holiday'] ) ) {
				$eventDetails[] = esc_html( $event['holiday'] );
			}
			if ( ! empty( $event['celeb'] ) ) {
				$eventDetails[] = esc_html( $event['celeb'] );
			}
			if ( ! empty( $event['other'] ) ) {
				$eventDetails[] = esc_html( $event['other'] );
			}
			$prev_month_flag = $prev_month == $eventMonth;

			if ( ! $prev_month_flag && $month_flag ) {
				$formatted_event_date = $this->date_converter->getConvertedDate( $eventDate, 'LLLL' );
				echo '<h4 class="mb-month-calendar-title m-t-0">' . esc_html( $formatted_event_date ) . ' ' . $this->today->format( 'Y' ) . '</h4>';
				echo '<hr class="w-80">';
			}

			// Only output if there are event details
			if ( ! empty( $eventDetails ) ) {
				echo '<article>';
				echo '<div>' . esc_html( $formatter ) . '</div>';
				echo '<div>' . implode( '<br>', $eventDetails ) . '</div>';
				echo '</article>';
			}
			$prev_month = $eventMonth;

		}
		echo '</div>';
	}

	/**
	 * rendering a day view
	 * all events for a current day
	 */
	public function render_today_calendar( $data ) {

		$content = '';
		foreach ( $data as $event ) {
			$date  = new \DateTime( $event['date'] );
			$month = $date->format( 'm' );
			$day   = $date->format( 'd' );

			
			if ( $month == $this->month && $day == $this->day ) {
				echo '<pre>';
				print_r( $event );
				echo '</pre>';
				$content .= '<div class="mb-today-calendar-content">dupa';

				if ( ! empty( $event['birthday'] ) ) {
					$content .= '<p class="text-big"><strong>' . esc_html( $event['birthday'] ) . '</strong></p>';
				}

				if ( ! empty( $event['nameDay'] ) ) {
					$content .= '<p  class="text-big"><strong>' . esc_html( $event['nameDay'] ) . '</strong></p>';
				}

				if ( ! empty( $event['celeb'] ) ) {
					$content .= '<p>' . esc_html( $event['celeb'] ) . '</p>';
				}
				if ( ! empty( $event['holiday'] ) ) {
					$content .= '<p>' . esc_html( mb_strtoupper( $event['holiday'] ) ) . '</p>';
				}
				if ( ! empty( $event['unusualDay'] ) ) {
					$content .= '<p>' . esc_html( $event['unusualDay'] ) . '</p>';
				}
				if ( ! empty( $event['proverb'] ) ) {
					$content .= '<p><i>' . esc_html( $event['proverb'] ) . '</i></p>';
				}
				if ( ! empty( $event['other'] ) ) {
					$content .= '<p>' . esc_html( $event['other'] ) . '</p>';
				}

				$content .= '</div>';
				echo $content ? $content : '';
			}
		}
	}

}