<?php

namespace FamilyCalendar;

/**
 * NetatmoAdmin class
 *
 * Responsible for creating admin settings page for Netatmo API credentials
 */
class NetatmoAdmin
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Add admin menu
        add_action('admin_menu', [$this, 'add_admin_menu']);
        
        // Register settings
        add_action('admin_init', [$this, 'register_settings']);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu()
    {
        add_submenu_page(
            'options-general.php',
            'Netatmo API Settings',
            'Netatmo API',
            'manage_options',
            'mb-netatmo-settings',
            [$this, 'render_settings_page']
        );
    }

    /**
     * Register settings
     */
    public function register_settings()
    {
        // Register a new section
        add_settings_section(
            'mb_netatmo_api_section',
            'Netatmo API Credentials',
            [$this, 'render_section_description'],
            'mb-netatmo-settings'
        );

        // Register settings fields
        register_setting('mb_netatmo_settings', 'mb_netatmo_client_id');
        register_setting('mb_netatmo_settings', 'mb_netatmo_client_secret');
        register_setting('mb_netatmo_settings', 'mb_netatmo_refresh_token');

        // Add settings fields
        add_settings_field(
            'mb_netatmo_client_id',
            'Client ID',
            [$this, 'render_client_id_field'],
            'mb-netatmo-settings',
            'mb_netatmo_api_section'
        );

        add_settings_field(
            'mb_netatmo_client_secret',
            'Client Secret',
            [$this, 'render_client_secret_field'],
            'mb-netatmo-settings',
            'mb_netatmo_api_section'
        );

        add_settings_field(
            'mb_netatmo_refresh_token',
            'Refresh Token',
            [$this, 'render_refresh_token_field'],
            'mb-netatmo-settings',
            'mb_netatmo_api_section'
        );
    }

    /**
     * Render section description
     */
    public function render_section_description()
    {
        echo '<p>Enter your Netatmo API credentials. You can obtain these by creating an app at <a href="https://dev.netatmo.com/" target="_blank">https://dev.netatmo.com/</a>.</p>';
        echo '<p>After creating an app, you\'ll need to generate a refresh token using the OAuth2 authorization flow.</p>';
    }

    /**
     * Render client ID field
     */
    public function render_client_id_field()
    {
        $client_id = get_option('mb_netatmo_client_id', '');
        echo '<input type="text" name="mb_netatmo_client_id" value="' . esc_attr($client_id) . '" class="regular-text" />';
    }

    /**
     * Render client secret field
     */
    public function render_client_secret_field()
    {
        $client_secret = get_option('mb_netatmo_client_secret', '');
        echo '<input type="password" name="mb_netatmo_client_secret" value="' . esc_attr($client_secret) . '" class="regular-text" />';
    }

    /**
     * Render refresh token field
     */
    public function render_refresh_token_field()
    {
        $refresh_token = get_option('mb_netatmo_refresh_token', '');
        echo '<input type="text" name="mb_netatmo_refresh_token" value="' . esc_attr($refresh_token) . '" class="regular-text" />';
        echo '<p class="description">This is the refresh token obtained from the OAuth2 authorization flow.</p>';
    }

    /**
     * Render settings page
     */
    public function render_settings_page()
    {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form action="options.php" method="post">
                <?php
                // Output security fields
                settings_fields('mb_netatmo_settings');
                
                // Output setting sections and fields
                do_settings_sections('mb-netatmo-settings');
                
                // Output save settings button
                submit_button('Save Settings');
                ?>
            </form>
        
        </div>
        <?php
    }
}