<?php

namespace FamilyCalendar;

/**
 * CalendarAllShortcode class
 * 
 * Responsible for rendering the calendar_all shortcode
 */
class CalendarAllShortcode
{
    private $year;

    /**
     * Constructor
     * 
     * @param string $year Current year
     */
    public function __construct($year)
    {
        $this->year = $year;
    }

    /**
     * Render the calendar_all shortcode
     * 
     * @return string HTML output
     */
    public function render()
    {
        wp_enqueue_style('mb-calendar-style');

        $read_json = new ReadJsonData();
        $data = get_option('mb_calendar_data', []);
        if (empty($data)) {
            $read_json = new ReadJsonData();
            $data = $read_json->get_json_data($this->year);
        }

        return $this->render_calendar($data, '', 'year');
    }

    /**
     * Render calendar HTML
     * 
     * @param array $data Calendar data
     * @param string $date Formatted date string
     * @param string $render_attr Render attribute
     * @return string HTML output
     */
    private function render_calendar($data, $date, $render_attr = 'main')
    {
        $render_content = new RenderHtml();

        ob_start();
        echo '<div class="mb-today-calendar">';

        if (is_array($data)) {
            switch ($render_attr) {
                case 'main':
                    echo '<h3>' . $date . '</h3>';
                    $render_content->render_today_calendar($data);
                    $render_content->render_calendar_events($data, 'month');
                    break;
                case 'today':
                    echo '<h3>' . $date . '</h3>';
                    $render_content->render_today_calendar($data);
                    break;
                case 'year':
                case 'month':
                    $render_content->render_calendar_events($data, $date, $render_attr);
                    break;
            }
        } else {
            echo '<p> no data! </p>';
        }
        echo '</div>';

        return ob_get_clean();
    }
}
