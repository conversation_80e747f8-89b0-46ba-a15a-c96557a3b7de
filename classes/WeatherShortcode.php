<?php

namespace FamilyCalendar;

/**
 * WeatherShortcode class
 *
 * Responsible for rendering weather data from API
 */
class WeatherShortcode
{
    private $date_converter;

    /**
     * Constructor
     *
     * @param DateConvert $date_converter Date converter instance
     */
    public function __construct($date_converter)
    {
        $this->date_converter = $date_converter;
    }

    /**
     * Render weather data from API
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render($atts = [])
    {
        wp_enqueue_style('mb-calendar-wheather-style');

        // Get default values from options
        $default_location = get_option('mb_weather_default_location');
        $default_days = get_option('mb_weather_default_days');
        $default_lang = get_option('mb_weather_default_lang');
        $api_key = get_option('mb_weather_api_key');

        // Default attributes
        $atts = shortcode_atts(
            array(
                'location' => $default_location,
                'days' => $default_days,
                'lang' => $default_lang,
            ),
            $atts,
            'mb_calendar_weather'
        );

        // Get current weather data first
        $current_api_url = 'http://api.weatherapi.com/v1/current.json?key=' . $api_key . '&q=' . urlencode($atts['location']) . '&lang=' . $atts['lang'] . '&aqi=no';
        $current_response = wp_remote_get($current_api_url);

        // Get forecast data
        $forecast_api_url = 'http://api.weatherapi.com/v1/forecast.json?key=' . $api_key . '&q=' . urlencode($atts['location']) . '&days=' . intval($atts['days']) . '&lang=' . $atts['lang'] . '&aqi=no';
        $forecast_response = wp_remote_get($forecast_api_url);

        // Start output buffer
        ob_start();

        // Check if both requests were successful
        if (is_wp_error($current_response) || is_wp_error($forecast_response)) {
            echo '<p>Błąd pobierania danych pogodowych: ';
        } else {
            // Parse JSON responses
            $current_data = json_decode(wp_remote_retrieve_body($current_response), true);
            $forecast_data = json_decode(wp_remote_retrieve_body($forecast_response), true);

            // Check if data is valid
            if (
                isset($current_data['location']) && isset($current_data['current']) &&
                isset($forecast_data['location']) && isset($forecast_data['forecast'])
            ) {
                // Merge the data - use current data for real-time info, forecast data for forecasts
                $weather_data = $forecast_data;
                $weather_data['current'] = $current_data['current'];

                // Get coordinates for sea temperature data
                $lat = $current_data['location']['lat'];
                $lon = $current_data['location']['lon'];

                // Get sea temperature data from Open-Meteo
                $sea_temp = $this->get_open_meteo_sea_temperature($lat, $lon);

                // Display weather data
                $this->render_weather_data($weather_data, $sea_temp);
            } else {
                // Display error if data is invalid (in Polish)
                echo '<div class="mb-weather-calendar">';
                echo '<h3>Pogoda</h3>';
                echo '<p>Błąd: Nie można pobrać danych pogodowych dla ' . esc_html($atts['location']) . '</p>';
                echo '</div>';
            }
        }

        // Return the output buffer content
        return ob_get_clean();
    }

    /**
     * Get sea temperature data from Open-Meteo API
     * 
     * @param float $lat Latitude
     * @param float $lon Longitude
     * @return float|null Sea temperature or null if not available
     */
    private function get_open_meteo_sea_temperature($lat, $lon)
    {
        // Open-Meteo API endpoint for sea temperature
        $api_url = 'https://marine-api.open-meteo.com/v1/marine?latitude=' . $lat . '&longitude=' . $lon . '&hourly=sea_surface_temperature';

        $response = wp_remote_get($api_url);

        if (is_wp_error($response)) {
            return null;
        }

        $data = json_decode(wp_remote_retrieve_body($response), true);

        // Check if data is valid and contains sea temperature
        if (isset($data['hourly']) && isset($data['hourly']['sea_surface_temperature']) && !empty($data['hourly']['sea_surface_temperature'])) {
            // Get the current hour's sea temperature
            $current_hour = (int)date('H');
            return $data['hourly']['sea_surface_temperature'][$current_hour];
        }

        return null;
    }

    /**
     * Render weather data HTML
     *
     * @param array $weather_data Weather data from API
     * @param float|null $sea_temp Sea temperature from Open-Meteo
     */
    private function render_weather_data($weather_data, $sea_temp = null)
    {
?>
        <div class="mb-weather-calendar">

            <div class="mb-weather-current">
                <img class="mb-weather-current-icon"
                    src="<?php echo esc_url('https:' . $weather_data['current']['condition']['icon']); ?>"
                    alt="<?php echo esc_attr($weather_data['current']['condition']['text']); ?>">
                <div class="mb-weather-current-temp">
                    <span><?php echo esc_html(str_replace('.', ',', $weather_data['current']['temp_c'])); ?></span><span class="mb-weather-current-unit">&deg;C</span>
                </div>
            </div>

            <div class="mb-weather-current-condition">
                <?php echo esc_html($weather_data['current']['condition']['text']); ?>
            </div>

            <?php $this->render_weather_details($weather_data); ?>

            <?php $this->render_astronomical_data($weather_data); ?>

            <?php $this->render_water_temperature($sea_temp); ?>

        </div>
    <?php
    }

    /**
     * Render weather details section
     *
     * @param array $weather_data Weather data from API
     */
    private function render_weather_details($weather_data)
    {
    ?>
        <div class="mb-weather-current-details">
            <div class="mb-weather-detail">
                <span class="mb-weather-detail-label">Odczuwalna: </span>
                <div>
                    <span class="mb-weather-detail-value"><?php echo esc_html(str_replace('.', ',', $weather_data['current']['feelslike_c'])); ?></span><span class="mb-weather-current-unit">&deg;C</span>
                </div>
            </div>
            <div class="mb-weather-detail">
                <span class="mb-weather-detail-label">Wilgotność: </span>
                <span class="mb-weather-detail-value"><?php echo esc_html($weather_data['current']['humidity']); ?>%</span>
            </div>
            <div class="mb-weather-detail">
                <span class="mb-weather-detail-label">Wiatr: </span>
                <span class="mb-weather-detail-value"><?php echo esc_html(str_replace('.', ',', $weather_data['current']['wind_kph'])); ?> km/h</span>
            </div>
        </div>
        <?php
    }

    /**
     * Render water temperature section
     *
     * @param float|null $water_temp Sea temperature
     */
    private function render_water_temperature($water_temp)
    {
        if ($water_temp !== null) :
        ?>
            <div class="mb-weather-marine">
                <div class="mb-weather-marine-label">Temperatura wody</div>
                <div class="mb-weather-marine-value">
                    <?php echo esc_html(str_replace('.', ',', $water_temp)); ?>&deg;C
                </div>
            </div>
        <?php
        endif;
    }

    /**
     * Render astronomical data section (sunrise and sunset)
     *
     * @param array $weather_data Weather data from API
     */
    private function render_astronomical_data($weather_data)
    {
        ?>
        <div class="mb-weather-astro">
            <div class="mb-weather-astro-item">
                <span class="mb-weather-astro-label">Wschód słońca</span>
                <span class="mb-weather-astro-value">
                    <?php
                    // Simply remove the AM part from sunrise time
                    $sunrise_time = $weather_data['forecast']['forecastday'][0]['astro']['sunrise'];

                    // Just remove the "AM" part since sunrise is always in the morning
                    echo '<img src="' . plugin_dir_url(dirname(__FILE__)) . 'assets/images/sunrise.svg" width="24" height="24" alt="Sunrise" class="mb-weather-astro-icon"> ';
                    echo esc_html(preg_replace('/\s+AM$/i', '', $sunrise_time));
                    ?>
                </span>
            </div>
            <div class="mb-weather-astro-item">
                <span class="mb-weather-astro-label">Zachód słońca</span>
                <span class="mb-weather-astro-value">
                    <?php
                    // Convert sunset time from AM/PM to 24-hour format
                    $sunset_time = $weather_data['forecast']['forecastday'][0]['astro']['sunset'];

                    // Manual conversion to avoid timezone issues
                    if (preg_match('/(\d+):(\d+)\s+(AM|PM)/i', $sunset_time, $matches)) {
                        $hour = intval($matches[1]);
                        $minute = $matches[2];
                        $ampm = strtoupper($matches[3]);

                        // Convert to 24-hour format
                        if ($ampm === 'PM' && $hour < 12) {
                            $hour += 12;
                        } elseif ($ampm === 'AM' && $hour === 12) {
                            $hour = 0;
                        }

                        // Format the time with sunset icon before it
                        echo '<img src="' . plugin_dir_url(dirname(__FILE__)) . 'assets/images/sunset.svg" width="24" height="24" alt="Sunset" class="mb-weather-astro-icon"> ';
                        echo esc_html(sprintf('%02d:%02d', $hour, $minute));
                    } else {
                        // Fallback to original format if conversion fails
                        echo esc_html($sunset_time);
                    }
                    ?>
                </span>
            </div>
        </div>
<?php
    }
}
