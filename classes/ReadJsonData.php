<?php

namespace FamilyCalendar;

class ReadJsonData {
	private $plugin_dir;

	public function __construct() {
		$this->plugin_dir = dirname( plugin_dir_path( __FILE__ ) );
		$today            = new \DateTime();

	}

	/**
	 * get a data to render a day and month view
	 */
	public function get_json_data( $year ) {

		$static_events   = $this->read_json_data( 'all' );
		$moveable_events = $this->read_json_data( $year );
		$merged_data     = $this->merge_json_data( $static_events, $moveable_events );

//		echo '<pre>';
//		print_r( $merged_data );
//		echo '</pre>';

		return $merged_data;

	}


	private function merge_json_data( $static, $moveable ) {
		$merged = [];

		foreach ( $static as $entry ) {
			$merged[ $entry['date'] ] = $entry;
		}

		foreach ( $moveable as $entry ) {
			$merged[ $entry['date'] ] = array_merge(
				$merged[ $entry['date'] ] ?? [],
				$entry
			);
		}

		return $merged;
	}


	/**
	 * read data from a json file
	 */
	private
	function read_json_data(
		$year
	) {

		$json_path = $this->plugin_dir . DIRECTORY_SEPARATOR . 'inc' . DIRECTORY_SEPARATOR . 'data-' . $year . '.json';

		if ( file_exists( $json_path ) ) {
			$data = json_decode( file_get_contents( $json_path ), true );

			return ( json_last_error() === JSON_ERROR_NONE ) ? $data : [];
		} else {
			return '<p class="mb-info">Nie znaleziono pliku z danymi</p>';
		}
	}


}