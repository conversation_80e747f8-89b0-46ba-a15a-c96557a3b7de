<?php

namespace FamilyCalendar;

/**
 * WeatherAdmin class
 *
 * Responsible for creating admin settings page for Weather API configuration
 */
class WeatherAdmin
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Add admin menu
        add_action('admin_menu', [$this, 'add_admin_menu']);
        
        // Register settings
        add_action('admin_init', [$this, 'register_settings']);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu()
    {
        add_submenu_page(
            'options-general.php',
            'Weather API Settings',
            'Weather API',
            'manage_options',
            'mb-weather-settings',
            [$this, 'render_settings_page']
        );
    }

    /**
     * Register settings
     */
    public function register_settings()
    {
        // Register a new section
        add_settings_section(
            'mb_weather_api_section',
            'Weather API Configuration',
            [$this, 'render_section_description'],
            'mb-weather-settings'
        );


        // Register settings fields
        register_setting('mb_weather_settings', 'mb_weather_api_key');
        register_setting('mb_weather_settings', 'mb_weather_default_location');
        register_setting('mb_weather_settings', 'mb_weather_default_days');
        register_setting('mb_weather_settings', 'mb_weather_default_lang');

        // Add settings fields for Weather API
        add_settings_field(
            'mb_weather_api_key',
            'API Key',
            [$this, 'render_api_key_field'],
            'mb-weather-settings',
            'mb_weather_api_section'
        );

        add_settings_field(
            'mb_weather_default_location',
            'Default Location',
            [$this, 'render_default_location_field'],
            'mb-weather-settings',
            'mb_weather_api_section'
        );

        add_settings_field(
            'mb_weather_default_days',
            'Default Forecast Days',
            [$this, 'render_default_days_field'],
            'mb-weather-settings',
            'mb_weather_api_section'
        );

        add_settings_field(
            'mb_weather_default_lang',
            'Default Language',
            [$this, 'render_default_lang_field'],
            'mb-weather-settings',
            'mb_weather_api_section'
        );
    }

    /**
     * Render section description
     */
    public function render_section_description()
    {
        echo '<p>Enter your Weather API configuration. You can obtain an API key by signing up at <a href="https://www.weatherapi.com/" target="_blank">https://www.weatherapi.com/</a>.</p>';
    }

    /**
     * Render API key field
     */
    public function render_api_key_field()
    {
        $api_key = get_option('mb_weather_api_key', '');
        echo '<input type="text" name="mb_weather_api_key" value="' . esc_attr($api_key) . '" class="regular-text" />';
        echo '<p class="description">Your Weather API key</p>';
    }

    /**
     * Render default location field
     */
    public function render_default_location_field()
    {
        $location = get_option('mb_weather_default_location', 'Alghero');
        echo '<input type="text" name="mb_weather_default_location" value="' . esc_attr($location) . '" class="regular-text" />';
        echo '<p class="description">Default location for weather data</p>';
    }

    /**
     * Render default days field
     */
    public function render_default_days_field()
    {
        $days = get_option('mb_weather_default_days', 3);
        echo '<select name="mb_weather_default_days">';
        for ($i = 1; $i <= 7; $i++) {
            echo '<option value="' . $i . '"' . selected($days, $i, false) . '>' . $i . '</option>';
        }
        echo '</select>';
        echo '<p class="description">Number of forecast days (1-7)</p>';
    }

    /**
     * Render default language field
     */
    public function render_default_lang_field()
    {
        $lang = get_option('mb_weather_default_lang', 'pl');
        $languages = [
            'pl' => 'Polish',
            'en' => 'English',
            'de' => 'German',
            'fr' => 'French',
            'es' => 'Spanish',
            'it' => 'Italian'
        ];
        
        echo '<select name="mb_weather_default_lang">';
        foreach ($languages as $code => $name) {
            echo '<option value="' . esc_attr($code) . '"' . selected($lang, $code, false) . '>' . esc_html($name) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">Default language for weather data</p>';
    }

    /**
     * Render settings page
     */
    public function render_settings_page()
    {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form action="options.php" method="post">
                <?php
                // Output security fields
                settings_fields('mb_weather_settings');
                
                // Output setting sections and fields
                do_settings_sections('mb-weather-settings');
                
                // Output save settings button
                submit_button('Save Settings');
                ?>
            </form>
            
        </div>
        <?php
    }
}
