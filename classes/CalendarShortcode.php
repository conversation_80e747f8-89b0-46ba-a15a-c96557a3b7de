<?php

namespace FamilyCalendar;

/**
 * CalendarShortcode class
 * 
 * Responsible for rendering the calendar shortcode
 */
class CalendarShortcode
{
    private $today;
    private $year;
    private $date_converter;

    /**
     * Constructor
     * 
     * @param \DateTime $today Today's date
     * @param string $year Current year
     * @param DateConvert $date_converter Date converter instance
     */
    public function __construct($today, $year, $date_converter)
    {
        $this->today = $today;
        $this->year = $year;
        $this->date_converter = $date_converter;
    }

    /**
     * Render the calendar shortcode
     * 
     * @return string HTML output
     */
    public function render()
    {
        wp_enqueue_style('mb-calendar-style');

        $read_json = new ReadJsonData();
        $data = get_option('mb_calendar_data', []);
        if (empty($data)) {
            $read_json = new ReadJsonData();
            $data = $read_json->get_json_data($this->year);
        }

        $formattedDate = $this->date_converter->getConvertedDate($this->today, 'EEEE, d MMMM yyyy');
        return $this->render_calendar($data, $formattedDate);
    }

    /**
     * Render calendar HTML
     * 
     * @param array $data Calendar data
     * @param string $date Formatted date string
     * @param string $render_attr Render attribute
     * @return string HTML output
     */
    private function render_calendar($data, $date, $render_attr = 'main')
    {
        $render_content = new RenderHtml();

        ob_start();
        echo '<div class="mb-today-calendar">';

        if (is_array($data)) {
            switch ($render_attr) {
                case 'main':
                    echo '<h3>' . $date . '</h3>';
                    $render_content->render_today_calendar($data);
                    $render_content->render_calendar_events($data, 'month');
                    break;
                case 'today':
                    echo '<h3>' . $date . '</h3>';
                    $render_content->render_today_calendar($data);
                    break;
                case 'year':
                case 'month':
                    $render_content->render_calendar_events($data, $date, $render_attr);
                    break;
            }
        } else {
            echo '<p> no data! </p>';
        }
        echo '</div>';

        return ob_get_clean();
    }
}
