<?php

namespace FamilyCalendar;

class DateConvert {
	/**
	 * Holds the single instance of this class
	 */
	private static $instance = null;

	/**
	 * Private constructor to prevent direct object creation
	 */
	private function __construct() {}

	/**
	 * Prevent cloning
	 */
	private function __clone() {
	}

	/**
	 * Prevent unserializing
	 */
	public function __wakeup() {
	}

	/**
	 * Returns the single instance of this class
	 */
	public static function getInstance() {
		if ( self::$instance === null ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Converting date to a local string
	 */
	public function getConvertedDate( $date, $pattern ) {
		$formatter = new \IntlDateFormatter(
			'pl_PL',
			\IntlDateFormatter::LONG,
			\IntlDateFormatter::NONE,
			'Europe/Warsaw',
			\IntlDateFormatter::GREGORIAN,
			$pattern
		);

		return $formatter->format( $date );
	}
}
