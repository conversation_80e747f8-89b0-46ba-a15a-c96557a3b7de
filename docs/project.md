# [MB] blog.mariusza - WordPress Custom Content Plugin

## Project Description

A modular WordPress plugin designed for personal blog management with specialized modules for tracking various life activities. The plugin provides custom post type management, data visualization, and administrative interfaces for personal data tracking including weights, electrical consumption, and events.

## Core Functionality

### 🏗️ **Modular Architecture**
- **Weights Module**: Personal weight tracking with goal management
- **Electro Module**: Electrical consumption monitoring
- **Events Module**: Personal event and milestone tracking

### 🎯 **Key Features**
- Custom shortcodes for frontend data display
- Administrative interfaces for data management
- Goal tracking and progress visualization
- Responsive design with custom CSS styling
- Polish language support with localized date formatting
- Modular autoloading system

## Plugin Architecture

### 📁 **File Structure**
```
mb-calendar/
├── mb-plugin.php                    # Main plugin file with autoloader
├── docs/
│   └── project.md                   # This documentation
├── assets/
│   └── css/                         # Module-specific stylesheets
│       ├── mb-plugin.css           # Global plugin styles
│       ├── mb-weights.css          # Weights module styles
│       ├── mb-electro.css          # Electro module styles
│       ├── mb-events.css           # Events module styles
│       ├── cars-admin-style.css    # Legacy cars admin styles
│       └── cars-shortcode-services.css # Legacy cars frontend styles
└── modules/                        # Modular plugin architecture
    ├── Electro/                    # Electrical consumption module
    │   ├── Electro.php            # Main module controller
    │   ├── ElectroAllHtml.php     # HTML rendering class
    │   └── ElectroQuery.php       # Database queries
    ├── Events/                     # Personal events module
    │   ├── Events.php             # Main module controller
    │   ├── EventsHtml.php         # HTML rendering class
    │   └── EventsQuery.php        # Database queries
    └── Weights/                    # Weight tracking module
        ├── Weights.php            # Main module controller
        ├── WeightsAdmin.php       # Admin interface & goal management
        ├── WeightsQuery.php       # Database queries & goal logic
        ├── WeightsGoalHTML.php    # Goal status rendering
        ├── WeightsSummaryHTML.php # Summary statistics rendering
        ├── WeightsPaginationHTML.php # Pagination controls
        ├── WeightsShortcodeHTML.php # Individual entry rendering
        └── WeightsShortcodeHTMLWithPrevious.php # Paginated entry rendering
```

## Module Details

### 🏋️ **Weights Module** (`modules/Weights/`)
**Purpose**: Personal weight tracking with goal management and progress visualization

**Custom Post Type**: `mpw` (My Personal Weight)
- **Meta Fields**: `mpw-date`, `mpw-weight` (stored as integer × 10 for precision)

**Admin Features**:
- Custom admin columns showing weight, date, and change indicators
- Goal management interface at `wp-admin/edit.php?post_type=mpw&page=weight-goals`
- Goal storage via WordPress options: `mb_weight_goal`, `mb_weight_goal_date`

**Shortcodes**:
- `[mb_weights_all]` - All weight measurements
- `[mb_weights_recent count="10"]` - Recent measurements with summary
- `[mb_weights_monthly]` - Monthly view with Polish date formatting
- `[mb_weights_paginated]` - Paginated view with navigation

**Goal Features**:
- Set target weight goals
- Visual progress indicators (color-coded distance to goal)
- Goal achievement tracking
- Integration with all shortcode displays

### ⚡ **Electro Module** (`modules/Electro/`)
**Purpose**: Electrical consumption monitoring

**Custom Post Types**: `strom` (electricity), `wärmepumpe` (heat pump)
- **Meta Fields**: `read-date`, `counter-wp`, `counter-s`

**Shortcodes**:
- `[mb_electro_all]` - All consumption data
- `[mb_electro_months]` - Monthly consumption analysis

**Features**:
- Multi-provider cost calculation (energis, süwag)
- Consumption trend analysis
- Cost comparison tools

### 📅 **Events Module** (`modules/Events/`)
**Purpose**: Personal event and milestone tracking

**Custom Post Type**: `events`
- **Meta Fields**: `mbs-date` for event dates

**Shortcodes**:
- `[mb_month_events]` - Monthly event overview

**Features**:
- Event date tracking and display
- Monthly event organization
- Custom event categorization

## Technical Implementation

### 🔧 **Core Architecture**
- **Namespace**: `PluginMB\Modules\{ModuleName}`
- **Autoloader**: PSR-4 compatible autoloading in main plugin file
- **Initialization**: Modules instantiated via `plugins_loaded` hook
- **Plugin File**: `mb-plugin.php` serves as the main entry point

### 🎨 **Frontend Integration**
- **CSS Registration**: Module-specific stylesheets registered but not auto-enqueued
- **Shortcode Pattern**: Each module registers multiple shortcodes for different views
- **Style Enqueuing**: Styles are enqueued only when shortcodes are used
- **Responsive Design**: All modules include responsive CSS styling

### 🛡️ **Security & Data Handling**
- **WordPress Security**: Follows WordPress security best practices
- **ABSPATH Check**: Plugin prevents direct access with `defined('ABSPATH') or die()`
- **Data Sanitization**: Input validation and sanitization throughout
- **WordPress Standards**: Follows WordPress coding standards and best practices

### 🌍 **Localization**
- **Text Domain**: `mb-plugin`
- **Polish Language**: Extensive Polish language support with proper date formatting
- **Date Formatting**: Custom Polish month names and date representations

## Dependencies

### WordPress Requirements
- **WordPress Version**: 5.0+
- **PHP Version**: 7.4+
- **Custom Post Types**: Plugin assumes CPTs are registered elsewhere or creates them
- **WordPress Core**: Uses standard WordPress hooks and functions

### External Dependencies
- **No Composer**: Plugin uses WordPress native functionality
- **No External APIs**: All functionality is self-contained
- **Self-Contained**: No external plugin dependencies required

## Usage for AI Agents

### 🤖 **Key Information for AI Assistance**

**Data Storage Patterns**:
- Weights: WordPress post meta with integer storage (multiply by 10)
- Goals: WordPress options table
- Events/Electro: Standard WordPress post meta
- All modules use WordPress native data storage

**Common Tasks**:
- Adding new shortcode parameters
- Modifying HTML output classes
- Extending admin interfaces
- Adding new goal-related features
- Customizing CSS styling
- Creating new modules following existing patterns

**Code Patterns**:
- Each module follows MVC-like pattern: Controller → Query → HTML
- Shortcodes accept attributes via `shortcode_atts()`
- HTML generation through dedicated classes
- Database queries via custom Query classes
- CSS styles registered but enqueued only when needed

**Important Notes**:
- Weight values stored as integers (multiply by 10 for decimal precision)
- Polish language strings throughout
- Responsive design considerations in CSS
- Goal functionality integrated across all weight displays
- Plugin uses PSR-4 autoloading for clean module organization

## Development Guidelines

### 🛠️ **Adding New Modules**
1. Create new directory in `modules/` folder
2. Follow naming convention: `ModuleName/ModuleName.php`
3. Use namespace: `PluginMB\Modules\ModuleName`
4. Register shortcodes in module constructor
5. Create separate Query and HTML classes for organization
6. Add CSS file in `assets/css/` with `mb-{module}.css` naming

### 🎨 **Styling Guidelines**
- Use BEM methodology for CSS classes
- Prefix all classes with `mb-` for plugin namespace
- Register styles but don't auto-enqueue (let shortcodes handle it)
- Ensure responsive design for all components

### 📝 **Code Standards**
- Follow WordPress coding standards
- Use proper escaping for all output
- Implement proper error handling
- Document all public methods
- Use meaningful variable and function names
