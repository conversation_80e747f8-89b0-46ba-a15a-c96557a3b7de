# [MB] blog.mariusza - WordPress Custom Content Plugin

## Project Description

A modular WordPress plugin designed for personal blog management with specialized modules for tracking various life activities. The plugin provides custom post type management, data visualization, and administrative interfaces for personal data tracking including weights, cars, electrical consumption, and events.

## Core Functionality

### 🏗️ **Modular Architecture**
- **Weights Module**: Personal weight tracking with goal management
- **Cars Module**: Vehicle management and service tracking
- **Electro Module**: Electrical consumption monitoring
- **Events Module**: Personal event and milestone tracking

### 🎯 **Key Features**
- Custom shortcodes for frontend data display
- Administrative interfaces for data management
- Goal tracking and progress visualization
- Responsive design with custom CSS styling
- Polish language support with localized date formatting
- AJAX functionality for dynamic content loading

## Plugin Architecture

### 📁 **File Structure**
```
mb-plugin/
├── mb-plugin.php                    # Main plugin file with autoloader
├── docs/
│   └── project.md                   # This documentation
├── assets/
│   ├── css/                         # Module-specific stylesheets
│   │   ├── mb-plugin.css           # Global plugin styles
│   │   ├── mb-weights.css          # Weights module styles
│   │   ├── mb-electro.css          # Electro module styles
│   │   ├── mb-events.css           # Events module styles
│   │   ├── cars-admin-style.css    # Cars admin styles
│   │   └── cars-shortcode-services.css # Cars frontend styles
│   └── js/
│       └── mb-cars-ajax.js         # AJAX functionality for cars
└── modules/                        # Modular plugin architecture
    ├── Cars/                       # Vehicle management module
    │   ├── Cars.php               # Main module controller
    │   ├── CustomQuery.php        # Database queries for cars
    │   ├── Sharedata.php          # Shared data/options
    │   └── Shortcode.php          # Frontend shortcode rendering
    ├── Electro/                    # Electrical consumption module
    │   ├── Electro.php            # Main module controller
    │   ├── ElectroAllHtml.php     # HTML rendering class
    │   └── ElectroQuery.php       # Database queries
    ├── Events/                     # Personal events module
    │   ├── Events.php             # Main module controller
    │   ├── EventsHtml.php         # HTML rendering class
    │   └── EventsQuery.php        # Database queries
    └── Weights/                    # Weight tracking module
        ├── Weights.php            # Main module controller
        ├── WeightsAdmin.php       # Admin interface & goal management
        ├── WeightsQuery.php       # Database queries & goal logic
        ├── WeightsGoalHTML.php    # Goal status rendering
        ├── WeightsSummaryHTML.php # Summary statistics rendering
        ├── WeightsPaginationHTML.php # Pagination controls
        ├── WeightsShortcodeHTML.php # Individual entry rendering
        └── WeightsShortcodeHTMLWithPrevious.php # Paginated entry rendering
```

## Module Details

### 🏋️ **Weights Module** (`modules/Weights/`)
**Purpose**: Personal weight tracking with goal management and progress visualization

**Custom Post Type**: `mpw` (My Personal Weight)
- **Meta Fields**: `mpw-date`, `mpw-weight` (stored as integer × 10 for precision)

**Admin Features**:
- Custom admin columns showing weight, date, and change indicators
- Goal management interface at `wp-admin/edit.php?post_type=mpw&page=weight-goals`
- Goal storage via WordPress options: `mb_weight_goal`, `mb_weight_goal_date`

**Shortcodes**:
- `[mb_weights_all]` - All weight measurements
- `[mb_weights_recent count="10"]` - Recent measurements with summary
- `[mb_weights_monthly]` - Monthly view with Polish date formatting
- `[mb_weights_paginated]` - Paginated view with navigation

**Goal Features**:
- Set target weight goals
- Visual progress indicators (color-coded distance to goal)
- Goal achievement tracking
- Integration with all shortcode displays

### 🚗 **Cars Module** (`modules/Cars/`)
**Purpose**: Vehicle management and service tracking

**Custom Post Type**: `cars` (managed via Pods framework)
- **Fields**: Current car flag, purchase date, price, mileage, engine specs, etc.

**Shortcodes**:
- `[mb_cars]` - Current car display with AJAX details
- `[mb_services]` - Service history
- `[mb_fuel]` - Fuel consumption tracking
- `[mb_all_services]` - Complete service overview

**Features**:
- AJAX-powered car details loading
- Service type categorization (maintenance, repair, parts, etc.)
- Fuel type tracking (gasoline, diesel, AdBlue)

### ⚡ **Electro Module** (`modules/Electro/`)
**Purpose**: Electrical consumption monitoring

**Custom Post Types**: `strom` (electricity), `wärmepumpe` (heat pump)
- **Meta Fields**: `read-date`, `counter-wp`, `counter-s`

**Shortcodes**:
- `[mb_electro_all]` - All consumption data
- `[mb_electro_months]` - Monthly consumption analysis

**Features**:
- Multi-provider cost calculation (energis, süwag)
- Consumption trend analysis
- Cost comparison tools

### 📅 **Events Module** (`modules/Events/`)
**Purpose**: Personal event and milestone tracking

**Custom Post Type**: `events`
- **Meta Fields**: `mbs-date` for event dates

**Shortcodes**:
- `[mb_month_events]` - Monthly event overview

## Technical Implementation

### 🔧 **Core Architecture**
- **Namespace**: `PluginMB\Modules\{ModuleName}`
- **Autoloader**: PSR-4 compatible autoloading in main plugin file
- **Initialization**: Modules instantiated via `plugins_loaded` hook

### 🎨 **Frontend Integration**
- **CSS Registration**: Module-specific stylesheets registered but not auto-enqueued
- **Shortcode Pattern**: Each module registers multiple shortcodes for different views
- **AJAX Support**: Cars module includes AJAX functionality for dynamic loading

### 🛡️ **Security & Data Handling**
- **Nonce Verification**: All form submissions protected
- **Capability Checks**: Admin functions require `manage_options`
- **Data Sanitization**: Input validation and sanitization throughout
- **WordPress Standards**: Follows WordPress coding standards and best practices

### 🌍 **Localization**
- **Text Domain**: `mb-plugin`
- **Polish Language**: Extensive Polish language support with proper date formatting
- **Date Formatting**: Custom Polish month names and date representations

## Dependencies

### WordPress Requirements
- **WordPress Version**: 6.0+
- **PHP Version**: 8.0+
- **Custom Post Types**: Plugin assumes CPTs are registered elsewhere
- **Pods Framework**: Cars module requires Pods plugin for data management

### External Dependencies
- **No Composer**: Plugin uses WordPress native functionality
- **No External APIs**: All functionality is self-contained
- **Optional**: Pods plugin for Cars module functionality

## Usage for AI Agents

### 🤖 **Key Information for AI Assistance**

**Data Storage Patterns**:
- Weights: WordPress post meta with integer storage (multiply by 10)
- Goals: WordPress options table
- Cars: Pods framework custom fields
- Events/Electro: Standard WordPress post meta

**Common Tasks**:
- Adding new shortcode parameters
- Modifying HTML output classes
- Extending admin interfaces
- Adding new goal-related features
- Customizing CSS styling

**Code Patterns**:
- Each module follows MVC-like pattern: Controller → Query → HTML
- Shortcodes accept attributes via `shortcode_atts()`
- HTML generation through dedicated classes
- Database queries via custom Query classes

**Important Notes**:
- Weight values stored as integers (multiply by 10 for decimal precision)
- Polish language strings throughout
- Responsive design considerations in CSS
- Goal functionality integrated across all weight displays
