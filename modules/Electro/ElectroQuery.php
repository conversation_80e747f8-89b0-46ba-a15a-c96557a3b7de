<?php
namespace PluginMB\Modules\Electro;

class ElectroQuery {
    public function query_custom_post_all()
    {
        $args = [
            'post_type' => 'strom',
            'post_per_page' => -1,
            'orderby' => 'meta_value',
            'meta_key' => 'read-date',
            'meta_type' => 'DATE',
            'order' => 'ASC',
        ];

        $query = new \WP_Query($args);
        $results = [];

        while ($query->have_posts()) {
            $query->the_post();
            $id = get_the_ID();
            $d = get_post_meta($id, 'read-date', true);
            $w = get_post_meta($id, 'counter-wp', true);
            $s = get_post_meta($id, 'counter-s', true);

            $results[$d] = [$w, $s];
        }
        wp_reset_postdata();
        return $results;
    }
    public function query_custom_post_months()
    {
        $args = [
            'post_type' => 'strom',
            'post_per_page' => -1,
            'orderby' => 'meta_value',
            'meta_key' => 'read-date',
            'order' => 'ASC',
        ];

        $query = new \WP_Query($args);
        $results = [];

        while ($query->have_posts()) {
            $query->the_post();
            $id = get_the_ID();
            $d = get_post_meta($id, 'read-date', true);
            $w = get_post_meta($id, 'counter-wp', true);
            $s = get_post_meta($id, 'counter-s', true);

            $date = new \DateTime($d);
            $date = $date->format('Y-m');

            $results[$date] = [$w, $s];

        }
        wp_reset_postdata();
        return $results;
    }
}
