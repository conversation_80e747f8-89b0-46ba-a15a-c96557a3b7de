<?php

namespace PluginMB\Modules\Electro;

class ElectroAllHtml
{
    private $results, $pattern;


    public function __construct($results, $pattern = 'm')
    {
        $this->results = $results;
        $this->pattern = $pattern;
    }

    public function render_shortcode_all_html()
    {
        wp_enqueue_style('mb-electro-style');

        ob_start();
        foreach ($this->results as $date => $data) {

            $content = null;
            $provider = null;

            $days = null;
            foreach ($data as $key => $value) {
                $comsumpsion = $value[1];
                $meter_reading = $value[0];
                $price = $value[2];
                $daily_consum = $value[3];
                $days = $value[4];
                $provider = $value[5];

                // render html table
                $content .= "
                            <div class='mb-electro-content-flex__header'>{$key}</div>
                            <div class='mb-electro-content-flex'>
                            <div class='mb-electro-content-flex__item'>
                                <span>stan:</span>
                                <span>{$meter_reading}<span>
                            </div>                            
                            <div class='mb-electro-content-flex__item'>
                                <span>zuzycie:</span>
                                <span>+{$comsumpsion} kWh<span>
                            </div>
                            <div class='mb-electro-content-flex__item'>
                                <span>koszt: </span>
                                <span>{$price} €</span>
                            </div>
                            <div class='mb-electro-content-flex__item'>
                                <span>dziennie: </span>
                                <span>{$daily_consum} kWh</span>
                            </div>                          
                            </div>
                        ";
            }
            $content .= '</;>';

            // date convert for a month&year or full date
            $date_to_convert = new \DateTime($date);
            $date_pattern = '';
            switch ($this->pattern) {
                case 'm':
                    $date_pattern = 'LLLL yyyy';
                    break;
                case 'd':
                    $date_pattern = 'EEEE, d MMMM yyyy';
                    break;
            }

            //render html container
            $date_to_display = $this->date_convert($date_to_convert, $date_pattern);
            echo "  <article class='mb-electro-container'>
                    <div>
                    	<p class='m-b-0 m-t-0'>{$date_to_display}</p>
                    	<p class='m-b-10 m-t-0'>dni pomiarowe: {$days} | dostawca: {$provider}</p>
                    </div>
                    {$content}
                    </article>";
        }

        $output = ob_get_clean();

        return $output;
    }

    private function date_convert($date, $pattern = 'EEEE, d MMMM yyyy')
    {
//        $date_time = new \DateTime($date, new \DateTimeZone('Europe/Moscow'));
        $formatter = new \IntlDateFormatter(
            'pl_PL',
            \IntlDateFormatter::LONG,
            \IntlDateFormatter::NONE,
            'Europe/Warsaw',
            \IntlDateFormatter::GREGORIAN,
            $pattern
        );

        return $formatter->format($date);
    }
}