<?php

namespace PluginMB\Modules\Electro;
class Electro
{

    protected $query;
    protected $post_types = ['wärmepumpe', 'strom'];
    protected $costs = [
        'energis' => [0.24, 0.42, 'energis'],
        'suewag' => [0.2210, 0.3081, 'süwag'],
    ];
    protected $costs_flat = [
        'energis' => [12.25, 14.26, 'energis'],
        'suewag' => [38.28, 30.81, 'süwag'],
    ];

    public function __construct()
    {
        $this->query = new ElectroQuery();

        add_shortcode(
            'mb_electro_all',
            [$this, 'electro_all_shortcode']);
        add_shortcode(
            'mb_electro_months',
            [$this, 'electro_months_shortcode']);
    }

    public function electro_all_shortcode()
    {
        $custom_query = $this->query->query_custom_post_all();
        $results = $this->data_to_shortcode_all($custom_query);
        $shortcode_all_html = new ElectroAllHtml($results, 'd');

        return $shortcode_all_html->render_shortcode_all_html();
    }

    public function electro_months_shortcode()
    {
        $custom_query = $this->query->query_custom_post_months();
        $results = $this->data_to_shortcode_all($custom_query, 'm');
        $shortcode_all_html = new ElectroAllHtml($results, 'm');

        return $shortcode_all_html->render_shortcode_all_html();
    }

    public function data_to_shortcode_all($result, $option = 'd')
    {
        $arr = [];
        $previous_data = null;

        foreach ($result as $i => $data) {
            $date = new \DateTime($i);
            foreach ($data as $key => $value) {

                // calculate a diference between the meter reading
                $diff = isset($previous_data[1][$key]) ? $value - $previous_data[1][$key] : 0;
                // calculate cost per meter read
                // checkin option 'd' - daily, 'm' - monthly
                $cost_temp = null;
                $provider = '';
                switch ($option) {
                    case 'd':
                        $provider = $this->electro_cost($date);
                        $cost_temp = $diff * $this->costs[$provider][$key];
                        break;

                    case 'm':
                        $provider = $this->electro_cost($date);
                        $cost_temp = ($diff * $this->costs[$provider][$key]) + $this->costs_flat[$provider][$key];
                        break;

                }
                $cost = number_format($cost_temp, 2, ',');
                // calculate days difference
                $previous_date = isset(($previous_data[0])) ? new \DateTime($previous_data[0]) : $date;
                $days = $date->diff($previous_date)->days;
                // calculate day avarage kWh
                $day_avg = $days ? number_format($diff / $days, 1, ',') : 0;

                $name = $this->post_types[$key];
                $arr[$i][$name] = [
                    $value,
                    $diff,
                    $cost,
                    $day_avg,
                    $days,
                    $this->costs[$provider][2]
                ];
            }
            $previous_data = [$i, $data];
        }

        return array_reverse($arr);
    }

    private function electro_cost($date)
    {
        $comparison_date = new \DateTime('2025-01-13');

        if ($date < $comparison_date) {
            return 'energis';
        } else {
        return 'suewag';
        }
    }
}