<?php

namespace PluginMB\Modules\Events;

class EventsHtml {

	private $today, $day, $month, $year;
	public function __construct() {
		$this->today = new \DateTime();
		$this->day = $this->today->format('d');
		$this->month = $this->today->format('m');
		$this->year = $this->today->format('Y');
	}

	public function render_month_events($events) {
		ob_start();
		echo '<div class="mbs-month">' . $this->date_convert($this->today, 'LLLL yyyy') . '</div>';
		echo '<div class="mbs-month-calendar">';
		echo '<hr>';

		foreach ($events as $event) {

			$name = $event['title'];
			$time = $event['time'];
			$logo = get_the_post_thumbnail($event['id'], [ 60, 60 ]);
			$tags = $event['terms']['post_tag']  ?? '';
			try {
				$event_date = new \DateTime($event['date']);
				$date_format = $this->date_convert($event_date, 'd MMMM, EEEE');
			} catch (\Exception $e) {
				continue;
			}

			$event_month = $event_date->format('m');
			$event_day = $event_date->format('d');
			$event_time = $event_date->format('H');
			$current_month = $this->month;
			$current_day = $this->day;			

			if ($event_month != $current_month) {
				continue;
			}
			
			

			echo '<article class="mbs-article">';

			// Left column for the logo
			echo '<div class="mbs-article__left">';
			// Display the logo with a larger size
			echo '<div class="mbs-logo">' . $logo . '</div>';
			echo '</div>';

			// Right column for the event details
			echo '<div class="mbs-article__right">';
			// Month title and time
			echo '<div class="mbs-month-title">' . esc_html($date_format);
			// Format the time if it exists
			if ($time) {
				// Convert the time to a more readable format
				$formatted_time = date('H:i', strtotime($time)); // Format to 'HH:MM'
				echo ' godz. ' . esc_html($formatted_time);
			}
			echo '</div>';

			// Event information
			echo '<div class="mbs-event__info">' . esc_html($name) . '</div>';
			echo '<div class="mbs-month-tag">[ ' . $tags . ' ]</div>';
			echo '</div>';

			echo '</article>';

		}
		echo '</div>';

		return ob_get_clean();
	}

	private function date_convert($date, $pattern = 'EEEE, d MMMM yyyy')
	{
		$formatter = new \IntlDateFormatter(
			'pl_PL',
			\IntlDateFormatter::LONG,
			\IntlDateFormatter::NONE,
			'Europe/Warsaw',
			\IntlDateFormatter::GREGORIAN,
			$pattern
		);

		// Convert the date to a timestamp
		if ($date instanceof \DateTime) {
			$timestamp = $date->getTimestamp();
		} else {
			$timestamp = strtotime($date);
			if (!$timestamp) {
				// Handle invalid date
				return 'Invalid date';
			}
		}

		return $formatter->format($date);
	}

}