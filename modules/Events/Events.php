<?php

namespace PluginMB\Modules\Events;

class Events {
	private $query;
	public function __construct() {
		$this->query = new EventsQuery();
		add_shortcode('mb_month_events', [$this, 'render_month_events']);
	}

	public function render_month_events() {

		wp_enqueue_style('mb-events-style');

		$query = new EventsQuery();
		$events = $query->get_all_events();
		$html = new EventsHtml();
		return $html->render_month_events($events);

//		ob_clean();
//		echo '<pre>';
//		print_r($events);
//		echo '</pre>';
//
//		return ob_get_clean();
	}


}