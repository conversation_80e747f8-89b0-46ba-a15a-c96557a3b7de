<?php

namespace PluginMB\Modules\Events;
class EventsQuery {

	private $query;
	public function custom_query($args)
	{
		$this->query = new \WP_Query($args);
		$results = [];

		while ($this->query->have_posts()) {
			$this->query->the_post();
			$id = get_the_ID();
			$results[] = [
				'id' => $id,
				'title' => substr(get_the_title(), 0, -13),
				'place' => get_post_meta($id, 'mbs-place', true),
				'date'  => get_post_meta($id, 'mbs-date', true),
				'time'  => get_post_meta($id, 'mbs-time', true),
				'type'  => get_post_meta($id, 'mbs-type', true),
				'link'  => get_post_meta($id, 'mbs-link', true),
				'notes' => get_post_meta($id, 'mbs-notes', true),
				'terms' => $this->get_post_terms($id),
			];
		}
		wp_reset_postdata();
		return $results;
	}

	private function get_post_terms($post_id)
	{
		// Get all taxonomies associated with the 'events' post type
		$taxonomies = get_object_taxonomies('events');
		$terms = [];

		foreach ($taxonomies as $taxonomy) {
			// Get terms for each taxonomy
			$terms_in_tax = get_the_terms($post_id, $taxonomy);
			if (!empty($terms_in_tax) && !is_wp_error($terms_in_tax)) {
				foreach ($terms_in_tax as $term) {
					$terms[$taxonomy] = $term->name;
				}
			}
		}
		return $terms;
	}

	private function get_post_tags($post_id)
	{
		$taxonomies = get_object_taxonomies('events');
		$tags = [];
		foreach ($taxonomies as $taxonomy) {
			// Get tags for each taxonomy
			$tags_in_tax = get_the_terms($post_id, $taxonomy);
			if (!empty($tags_in_tax) && !is_wp_error($tags_in_tax)) {
				foreach ($tags_in_tax as $tag) {
					$tag_name = $tag->taxonomy;
					$tags[] = $tag_name == 'post_tag' ? $tag->name : '';
				}
			}
		}
		return $tags;
	}

	public function get_all_events()
	{
		$args = [
			'post_type' => 'events',
			'posts_per_page' => -1,
			'order' => 'ASC',
			'meta_key' => 'mbs-date',
			'meta_type' => 'DATE',
			'orderby' => 'meta_value',

		];
		return $this->custom_query($args);
	}
}