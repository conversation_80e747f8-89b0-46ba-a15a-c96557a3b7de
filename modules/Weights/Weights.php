<?php

namespace PluginMB\Modules\Weights;

class Weights {
    protected $query;
    protected $admin;

    public function __construct() {
        $this->query = new WeightsQuery();

        // Initialize admin functionality
        if (is_admin()) {
            $this->admin = new WeightsAdmin();
        }

        // Register all shortcodes
        add_shortcode('mb_weights_all', [$this, 'weights_all_shortcode']);
        add_shortcode('mb_weights_recent', [$this, 'weights_recent_shortcode']);
        add_shortcode('mb_weights_monthly', [$this, 'weights_monthly_shortcode']);
        add_shortcode('mb_weights_paginated', [$this, 'weights_paginated_shortcode']);
    }

    /**
     * Original shortcode - show all weights
     */
    public function weights_all_shortcode() {
        wp_enqueue_style('mb-weights-style');

        $results = $this->query->get_all_weights();
        $previous_weight = 0;
        $content = [];

        foreach ($results as $key => $value) {
            $weight = $value['weight'];
            $renderHTML = new WeightsShortcodeHTML($key, $weight, $previous_weight, $this->query);
            $content[] = $renderHTML->generate_content();
            $previous_weight = $weight;
        }

        $content = array_reverse($content);
        return implode("\n", $content);
    }

    /**
     * Recent weights shortcode with summary
     * Usage: [mb_weights_recent count="10"] or [mb_weights_recent count="30"]
     */
    public function weights_recent_shortcode($atts) {
        wp_enqueue_style('mb-weights-style');

        $atts = shortcode_atts([
            'count' => 10
        ], $atts);

        $count = intval($atts['count']);
        $results = $this->query->get_recent_weights($count);

        if (empty($results)) {
            return '<p>Brak danych o wadze.</p>';
        }

        // Generate summary
        $summary_html = new WeightsSummaryHTML(
            $results,
            "Ostatnie {$count} pomiary",
            $this->query
        );
        
        $content = $summary_html->generate_summary();

        // Generate individual entries
        $previous_weight = 0;
        $entries = [];
        
        foreach ($results as $key => $value) {
            $weight = $value['weight'];
            $renderHTML = new WeightsShortcodeHTML($key, $weight, $previous_weight, $this->query);
            $entries[] = $renderHTML->generate_content();
            $previous_weight = $weight;
        }

        $content .= '<div class="mb-weights-list">';
        $content .= implode("\n", array_reverse($entries));
        $content .= '</div>';

        return $content;
    }

    /**
     * Monthly summary shortcode with Polish dates
     * Usage: [mb_weights_monthly] (defaults to current month/year)
     * or [mb_weights_monthly year="2024" month="12"]
     */
    public function weights_monthly_shortcode($atts) {
        wp_enqueue_style('mb-weights-style');

        // Get current month and year from URL parameters or use current date
        $current_year = isset($_GET['weight_year']) ? intval($_GET['weight_year']) : date('Y');
        $current_month = isset($_GET['weight_month']) ? intval($_GET['weight_month']) : date('n');

        $atts = shortcode_atts([
            'year' => $current_year,
            'month' => $current_month
        ], $atts);

        $year = intval($atts['year']);
        $month = intval($atts['month']);

        $results = $this->query->get_monthly_weights($year, $month);
        $available_months = $this->query->get_available_months();

        if (empty($results)) {
            $polish_month_name = $this->get_polish_month_name($month, $year);
            return '<p style="text-align: center;">Brak danych o wadze za ' . $polish_month_name . '.</p>';
        }

        $polish_month_name = $this->get_polish_month_name($month, $year);
        
        // Generate month navigation with Polish names
        $content = '<div class="mb-month-navigation">';
        $content .= '<div class="mb-month-selector">';
        
        foreach ($available_months as $available) {
            $is_current = ($available->year == $year && $available->month == $month);
            $month_url = add_query_arg([
                'weight_year' => $available->year,
                'weight_month' => $available->month
            ], get_permalink());
            
            $class = $is_current ? 'mb-month-link current' : 'mb-month-link';
            $display_name = $this->get_polish_month_name_short($available->month) . ' ' . $available->year;
            $content .= '<a href="' . esc_url($month_url) . '" class="' . $class . '">' . $display_name . '</a>';
        }
        $content .= '</div></div>';

        // Generate summary with Polish formatting
        $summary_html = new WeightsSummaryHTML(
            $results,
            $polish_month_name,
            $this->query
        );
        
        $content .= $summary_html->generate_summary();

        return $content;
    }

    /**
     * Paginated weights shortcode - FIXED VERSION
     * Usage: [mb_weights_paginated]
     */
    public function weights_paginated_shortcode($atts) {
        wp_enqueue_style('mb-weights-style');

        $atts = shortcode_atts([
            'per_page' => 10
        ], $atts);

        $per_page = intval($atts['per_page']);
        $current_page = isset($_GET['weight_page']) ? max(1, intval($_GET['weight_page'])) : 1;

        $results = $this->query->get_paginated_weights($current_page, $per_page);
        $data = $results['data'];
        $pagination = $results['pagination'];

        if (empty($data)) {
            return '<p>Brak danych o wadze.</p>';
        }

        $content = '<div class="mb-weights-paginated">';
        $content .= '<h3>wszystkie pomiary</h3>';

        // Generate individual entries with proper previous weight calculation
        $entries = [];
        
        foreach ($data as $date => $value) {
            $weight = $value['weight'];
            $id = $value['id'];
            
            // Use the new HTML class that properly calculates previous weight
            $renderHTML = new WeightsShortcodeHTMLWithPrevious($date, $weight, $this->query, $id);
            $entries[] = $renderHTML->generate_content();
        }

        $content .= '<div class="mb-weights-list">';
        $content .= implode("\n", $entries);
        $content .= '</div>';

        // Add pagination
        $pagination_html = new WeightsPaginationHTML($pagination);
        $content .= $pagination_html->generate_pagination();

        $content .= '</div>';

        return $content;
    }

    /**
     * Get Polish month name in nominative case (for standalone month display)
     */
    private function get_polish_month_name($month, $year) {
        $polish_months = [
            1 => 'Styczeń',
            2 => 'Luty', 
            3 => 'Marzec',
            4 => 'Kwiecień',
            5 => 'Maj',
            6 => 'Czerwiec',
            7 => 'Lipiec',
            8 => 'Sierpień',
            9 => 'Wrzesień',
            10 => 'Październik',
            11 => 'Listopad',
            12 => 'Grudzień'
        ];
        
        return $polish_months[$month] . ' ' . $year;
    }

    /**
     * Get Polish month name short version
     */
    private function get_polish_month_name_short($month) {
        $polish_months_short = [
            1 => 'Sty',
            2 => 'Lut', 
            3 => 'Mar',
            4 => 'Kwi',
            5 => 'Maj',
            6 => 'Cze',
            7 => 'Lip',
            8 => 'Sie',
            9 => 'Wrz',
            10 => 'Paź',
            11 => 'Lis',
            12 => 'Gru'
        ];
        
        return $polish_months_short[$month];
    }

    /**
     * Format date in Polish format
     */
    private function format_polish_date($date) {
        $timestamp = strtotime($date);
        $day = date('j', $timestamp);
        $month = date('n', $timestamp);
        $year = date('Y', $timestamp);
        
        $polish_months_genitive = [
            1 => 'stycznia',
            2 => 'lutego', 
            3 => 'marca',
            4 => 'kwietnia',
            5 => 'maja',
            6 => 'czerwca',
            7 => 'lipca',
            8 => 'sierpnia',
            9 => 'września',
            10 => 'października',
            11 => 'listopada',
            12 => 'grudnia'
        ];
        
        return $day . ' ' . $polish_months_genitive[$month] . ' ' . $year;
    }
}
