<?php

namespace PluginMB\Modules\Weights;

class WeightsSummaryHTML {

    private $data;
    private $title;
    private $query;

    public function __construct($data, $title = '', $query = null) {
        $this->data = $data;
        $this->title = $title;
        $this->query = $query ?: new WeightsQuery();
    }

    public function generate_summary() {
        if (empty($this->data)) {
            return '<div class="mb-weights-summary"><p>brak danych za ten okres</p></div>';
        }

        // Debug: Check if we have the required data structure
        if (!isset($this->data) || !is_array($this->data)) {
            return '<div class="mb-weights-summary"><p>Błąd: nieprawidłowe dane</p></div>';
        }

        $first_entry = reset($this->data);
        $last_entry = end($this->data);
        $stats = $this->calculate_stats();
        
        $html = '<div class="mb-weights-summary">';
        
        if ($this->title) {
            $html .= '<h3 class="mb-summary-title">' . esc_html($this->title) . '</h3>';
        }
        
        $html .= '<div class="mb-summary-stats">';
        $html .= '<div class="mb-stat-item">';
        $html .= '<span class="mb-stat-label">pierwszy pomiar</span>';
        $html .= '<span class="mb-stat-value">' . number_format($first_entry['weight'] / 10, 1, ',') . ' kg</span>';
        $html .= '<span class="mb-stat-date">(' . date('Y-m-d', strtotime($first_entry['date'])) . ')</span>';
        $html .= '</div>';

        $html .= '<div class="mb-stat-item">';
        $html .= '<span class="mb-stat-label">ostatni pomiar</span>';
        $html .= '<span class="mb-stat-value">' . number_format($last_entry['weight'] / 10, 1, ',') . ' kg</span>';
        $html .= '<span class="mb-stat-date">(' . date('Y-m-d', strtotime($last_entry['date'])) . ')</span>';
        $html .= '</div>';

        $html .= '<div class="mb-stat-item mb-stat-full-row">';
        $html .= '<span class="mb-stat-label">średnia waga:</span>';
        $html .= '<span class="mb-stat-value">' . number_format($stats['average'] / 10, 1, ',') . ' kg</span>';
        $html .= '<span class="mb-stat-date">(' . $stats['count'] . ' pomiarów)</span>';
        $html .= '</div>';

        $html .= '<div class="mb-stat-item mb-stat-full-row">';
        $diff = ($last_entry['weight'] - $first_entry['weight']) / 10;
        $class = $diff > 0 ? 'positive' : ($diff < 0 ? 'negative' : 'neutral');
        $sign = $diff > 0 ? '+' : '';
        $html .= '<span class="mb-stat-value ' . $class . '">' . $sign . number_format($diff, 1, ',') . ' kg</span>';
        $html .= '</div>';

        $html .= '</div>'; // close mb-summary-stats

        // Add goal information if available
        if (method_exists($this->query, 'get_current_goal') && class_exists('PluginMB\Modules\Weights\WeightsGoalHTML')) {
            $goal_weight = $this->query->get_current_goal();
            if ($goal_weight) {
                $goal_info = $this->query->calculate_distance_to_goal($last_entry['weight']);
                if ($goal_info) {
                    $goal_html = new WeightsGoalHTML($goal_info, $last_entry['weight'], $goal_weight);
                    $html .= $goal_html->generate_goal_status();
                }
            }
        }

        $html .= 'goal_weight: '. $goal_weight . '</div>'; // close mb-weights-summary

        return $html;
    }

    private function calculate_stats() {
        $weights = array_column($this->data, 'weight');
        $total = array_sum($weights);
        $count = count($weights);
        
        return [
            'average' => $count > 0 ? $total / $count : 0,
            'min' => min($weights),
            'max' => max($weights),
            'count' => $count
        ];
    }
}
