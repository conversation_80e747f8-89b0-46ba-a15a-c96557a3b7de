<?php

namespace PluginMB\Modules\Weights;

class WeightsGoalHTML {
    
    private $goal_info;
    private $current_weight;
    private $goal_weight;
    
    public function __construct($goal_info, $current_weight, $goal_weight) {
        $this->goal_info = $goal_info;
        $this->current_weight = $current_weight;
        $this->goal_weight = $goal_weight;
    }
    
    /**
     * Generate goal status HTML for display in shortcodes
     */
    public function generate_goal_status() {
        if (!$this->goal_info || !$this->current_weight || !$this->goal_weight) {
            return '';
        }
        
        $distance = $this->goal_info['distance'];
        $distance_abs = $this->goal_info['distance_abs'];
        $above_goal = $this->goal_info['above_goal'];
        $below_goal = $this->goal_info['below_goal'];
        $at_goal = $this->goal_info['at_goal'];
        
        $current_weight_formatted = number_format($this->current_weight / 10, 1, ',');
        $goal_weight_formatted = number_format($this->goal_weight / 10, 1, ',');
        
        $html = '<div class="mb-weight-goal-status">';
        $html .= '<div class="goal-info">';
        $html .= '<p><strong>Cel:</strong> ' . $goal_weight_formatted . ' kg</p>';
        $html .= '<p><strong>Aktualna waga:</strong> ' . $current_weight_formatted . ' kg</p>';
        
        if ($at_goal) {
            $html .= '<p class="goal-achieved"><strong>🎉 Cel osiągnięty!</strong></p>';
        } else {
            $status_class = $above_goal ? 'above-goal' : 'below-goal';
            $sign = $above_goal ? '+' : '';
            
            $html .= '<p class="goal-distance ' . $status_class . '">';
            $html .= '<strong>Dystans do celu:</strong> ';
            $html .= '<span class="distance-value">' . $sign . number_format($distance, 1, ',') . ' kg ';
            $html .= '</p>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Generate compact goal status for individual weight entries
     */
    public function generate_compact_goal_status($entry_weight) {
        if (!$this->goal_weight) {
            return '';
        }
        
        $goal_info = $this->calculate_distance_for_weight($entry_weight);
        
        if (!$goal_info) {
            return '';
        }
        
        $distance = $goal_info['distance'];
        $above_goal = $goal_info['above_goal'];
        $below_goal = $goal_info['below_goal'];
        $at_goal = $goal_info['at_goal'];
        
        if ($at_goal) {
            return '<span class="goal-status at-goal">Cel osiągnięty</span>';
        }
        
        $status_class = $above_goal ? 'above-goal' : 'below-goal';
        $sign = $above_goal ? '+' : '';
        
        return '<span class="goal-status ' . $status_class . '">' . 
               $sign . number_format($distance, 1, ',') . ' kg ' . 
               '</span>';
    }
    
    /**
     * Calculate distance to goal for a specific weight
     */
    private function calculate_distance_for_weight($weight) {
        if (!$this->goal_weight || !$weight) {
            return false;
        }
        
        $distance = ($weight - $this->goal_weight) / 10;
        
        return [
            'distance' => $distance,
            'distance_abs' => abs($distance),
            'above_goal' => $distance > 0,
            'below_goal' => $distance < 0,
            'at_goal' => $distance == 0
        ];
    }
}
