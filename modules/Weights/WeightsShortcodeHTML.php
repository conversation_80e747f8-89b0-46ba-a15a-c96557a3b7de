<?php

namespace PluginMB\Modules\Weights;

class WeightsShortcodeHTML
{

    private $key, $weight, $weight_diff, $previous_weight, $query;

    public function __construct($key, $weight, $previous_weight, $query = null)
    {
        $this->key = $key;
        $this->weight = $weight;
        $this->previous_weight = $previous_weight;
        $this->query = $query ?: new WeightsQuery();
    }

    public function generate_content()
    {
        $formated_weight = $this->format_weight();
        $formated_weight_diff = $this->format_weight_difference();
        $icon = $this->get_icon_and_color();

        return "<div class='mb-weights'>
          <div class='mb-weights__date'>{$this->key}</div>
          <div class='mb-weights__weight'>{$formated_weight} kg</div>
          <div class='mb-weights__diff'>{$formated_weight_diff}</div>
          <div class='mb-weights__icon'>{$icon}</div>
    </div>";
    }

    private function calculate_weight_diff()
    {
      $diff = ((int)$this->weight - (int)$this->previous_weight) / 10;
      return $diff < 20 ? $diff : 0;
    }

    private function format_weight()
    {
        return number_format($this->weight/10, 1, ',' ) ;
    }

    private function format_weight_difference()
    {
        $diff = $this->calculate_weight_diff();

        if ($diff > 0 ) {
            return '+' . number_format($diff, 1, ',' ) . ' kg';
        } elseif ($diff < 0 ) {
            return number_format($diff, 1, ',' ) . ' kg';
        } else {
            return '';
        }
    }

    private function get_icon_and_color()
    {
        $diff = $this->calculate_weight_diff();
        if ($diff > 0) {
            return
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" style="fill: red; text-align: center; --darkreader-inline-fill: #ff1313;" aria-hidden="true" focusable="false" data-darkreader-inline-fill=""><path d="M3.445 16.505a.75.75 0 001.06.05l5.005-4.55 4.024 3.521 4.716-4.715V14h1.5V8.25H14v1.5h3.19l-3.724 3.723L9.49 9.995l-5.995 5.45a.75.75 0 00-.05 1.06z"></path></svg>';
        } elseif ($diff < 0) {
            return
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" style="fill: green; text-align: center; --darkreader-inline-fill: #7dff7d;" aria-hidden="true" focusable="false" data-darkreader-inline-fill=""><path d="M4.195 8.245a.75.75 0 011.06-.05l5.004 4.55 4.025-3.521L19 13.939V10.75h1.5v5.75h-5.75V15h3.19l-3.724-3.723-3.975 3.478-5.995-5.45a.75.75 0 01-.051-1.06z"></path></svg>';
        } else {
            return '';
        }
    }
}
