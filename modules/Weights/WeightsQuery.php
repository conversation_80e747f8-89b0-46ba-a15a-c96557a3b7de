<?php

namespace PluginMB\Modules\Weights;

class WeightsQuery {
    
    public function get_all_weights() {
        $args = [
            'post_type'      => 'mpw',
            'posts_per_page' => -1,
            'order'          => 'ASC',
            'meta_key'       => 'mpw-date',
            'orderby'        => 'meta_value',
            'meta_type'      => 'DATE'
        ];

        return $this->execute_query($args);
    }

    /**
     * Get recent measurements (last N entries)
     */
    public function get_recent_weights($count = 10) {
        $args = [
            'post_type'      => 'mpw',
            'posts_per_page' => $count,
            'order'          => 'DESC',
            'meta_key'       => 'mpw-date',
            'orderby'        => 'meta_value',
            'meta_type'      => 'DATE'
        ];

        return $this->execute_query($args, true); // reverse for chronological order
    }

    /**
     * Get paginated weights
     */
    public function get_paginated_weights($page = 1, $per_page = 10) {
        $args = [
            'post_type'      => 'mpw',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'order'          => 'DESC',
            'meta_key'       => 'mpw-date',
            'orderby'        => 'meta_value',
            'meta_type'      => 'DATE'
        ];

        $query = new \WP_Query($args);
        $results = [];
        $pagination_info = [
            'total_posts' => $query->found_posts,
            'total_pages' => $query->max_num_pages,
            'current_page' => $page,
            'per_page' => $per_page
        ];

        while ($query->have_posts()) {
            $query->the_post();
            $id = get_the_ID();
            $date = get_post_meta($id, 'mpw-date', true);
            
            $results[$date] = [
                'id'     => $id,
                'date'   => $date,
                'weight' => get_post_meta($id, 'mpw-weight', true),
            ];
        }
        wp_reset_postdata();

        return [
            'data' => $results,
            'pagination' => $pagination_info
        ];
    }

    /**
     * Get previous weight for a specific date
     */
    public function get_previous_weight($current_date, $current_id = null) {
        $args = [
            'post_type'      => 'mpw',
            'posts_per_page' => 1,
            'order'          => 'DESC',
            'meta_key'       => 'mpw-date',
            'orderby'        => 'meta_value',
            'meta_type'      => 'DATE',
            'meta_query'     => [
                [
                    'key'     => 'mpw-date',
                    'value'   => $current_date,
                    'compare' => '<',
                    'type'    => 'DATE'
                ]
            ]
        ];

        if ($current_id) {
            $args['post__not_in'] = [$current_id];
        }

        $query = new \WP_Query($args);
        
        if ($query->have_posts()) {
            $query->the_post();
            $previous_weight = get_post_meta(get_the_ID(), 'mpw-weight', true);
            wp_reset_postdata();
            return $previous_weight ? intval($previous_weight) : 0;
        }
        
        wp_reset_postdata();
        return 0;
    }

    /**
     * Get monthly weights summary
     */
    public function get_monthly_weights($year = null, $month = null) {
        if (!$year) $year = date('Y');
        if (!$month) $month = date('m');
        
        $start_date = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));

        $args = [
            'post_type'      => 'mpw',
            'posts_per_page' => -1,
            'order'          => 'ASC',
            'meta_key'       => 'mpw-date',
            'orderby'        => 'meta_value',
            'meta_type'      => 'DATE',
            'meta_query'     => [
                [
                    'key'     => 'mpw-date',
                    'value'   => [$start_date, $end_date],
                    'compare' => 'BETWEEN',
                    'type'    => 'DATE'
                ]
            ]
        ];

        return $this->execute_query($args);
    }

    /**
     * Get available months/years for navigation
     */
    public function get_available_months() {
        global $wpdb;
        
        $results = $wpdb->get_results("
            SELECT DISTINCT 
                YEAR(STR_TO_DATE(meta_value, '%Y-%m-%d')) as year,
                MONTH(STR_TO_DATE(meta_value, '%Y-%m-%d')) as month,
                DATE_FORMAT(STR_TO_DATE(meta_value, '%Y-%m-%d'), '%Y-%m') as year_month
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = 'mpw-date' 
            AND p.post_type = 'mpw'
            AND p.post_status = 'publish'
            ORDER BY year DESC, month DESC
        ");

        return $results;
    }

    /**
     * Execute query and format results
     */
    private function execute_query($args, $reverse = false) {
        $query = new \WP_Query($args);
        $results = [];

        while ($query->have_posts()) {
            $query->the_post();
            $id = get_the_ID();
            $date = get_post_meta($id, 'mpw-date', true);

            $results[$date] = [
                'id'     => $id,
                'date'   => $date,
                'weight' => get_post_meta($id, 'mpw-weight', true),
            ];
        }
        wp_reset_postdata();

        if ($reverse) {
            $results = array_reverse($results, true);
        }

        return $results;
    }

    /**
     * Get current weight goal
     */
    public function get_current_goal() {
        return get_option('mb_weight_goal', false);
    }

    /**
     * Get goal date
     */
    public function get_goal_date() {
        return get_option('mb_weight_goal_date', false);
    }

    /**
     * Calculate distance to goal for a given weight
     */
    public function calculate_distance_to_goal($weight) {
        $goal = $this->get_current_goal();

        if (!$goal || !$weight) {
            return false;
        }

        // Both weights are in internal format (multiplied by 10)
        $distance = ($weight - $goal) / 10;

        return [
            'distance' => $distance,
            'distance_abs' => abs($distance),
            'above_goal' => $distance > 0,
            'below_goal' => $distance < 0,
            'at_goal' => $distance == 0
        ];
    }

    /**
     * Get latest weight with goal information
     */
    public function get_latest_weight_with_goal() {
        $args = [
            'post_type' => 'mpw',
            'posts_per_page' => 1,
            'order' => 'DESC',
            'meta_key' => 'mpw-date',
            'orderby' => 'meta_value',
            'meta_type' => 'DATE'
        ];

        $query = new \WP_Query($args);

        if ($query->have_posts()) {
            $query->the_post();
            $weight = get_post_meta(get_the_ID(), 'mpw-weight', true);
            $date = get_post_meta(get_the_ID(), 'mpw-date', true);
            wp_reset_postdata();

            if ($weight) {
                $goal_info = $this->calculate_distance_to_goal($weight);

                return [
                    'weight' => intval($weight),
                    'date' => $date,
                    'goal_info' => $goal_info
                ];
            }
        }

        wp_reset_postdata();
        return false;
    }
}
