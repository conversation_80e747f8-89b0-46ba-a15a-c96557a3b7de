<?php

namespace PluginMB\Modules\Weights;

class WeightsAdmin {

    public function __construct() {
        // Add custom columns to mpw post type admin list
        add_filter('manage_mpw_posts_columns', [$this, 'add_custom_columns']);

        // Populate custom columns with data
        add_action('manage_mpw_posts_custom_column', [$this, 'populate_custom_columns'], 10, 2);

        // Make custom columns sortable
        add_filter('manage_edit-mpw_sortable_columns', [$this, 'make_columns_sortable']);

        // Handle sorting for custom columns
        add_filter('request', [$this, 'handle_custom_column_sorting']);

        // Optional: Add custom CSS for admin columns
        add_action('admin_head', [$this, 'admin_column_styles']);

        // Add admin menu for weight goals
        add_action('admin_menu', [$this, 'add_weight_goals_menu']);

        // Handle goal form submissions
        add_action('admin_post_save_weight_goal', [$this, 'handle_save_weight_goal']);
        add_action('admin_post_delete_weight_goal', [$this, 'handle_delete_weight_goal']);
    }

    /**
     * Add custom columns to the mpw post type admin list
     */
    public function add_custom_columns($columns) {
        // Remove default date column
        unset($columns['date']);
        
        // Add custom columns
        $new_columns = [];
        $new_columns['cb'] = $columns['cb']; // Keep checkbox
        $new_columns['title'] = $columns['title']; // Keep title
        $new_columns['mpw_date'] = __('Weight Date', 'mb-plugin');
        $new_columns['mpw_weight'] = __('Weight (kg)', 'mb-plugin');
        $new_columns['weight_change'] = __('Change', 'mb-plugin');
        $new_columns['date'] = __('Created', 'mb-plugin'); // Add back created date but rename
        
        return $new_columns;
    }

    /**
     * Populate custom columns with data
     */
    public function populate_custom_columns($column_name, $post_id) {
        switch ($column_name) {
            case 'mpw_date':
                $date = get_post_meta($post_id, 'mpw-date', true);
                if ($date) {
                    echo '<strong>' . date('Y-m-d', strtotime($date)) . '</strong>';
                } else {
                    echo '<span style="color: #999;">—</span>';
                }
                break;

            case 'mpw_weight':
                $weight = get_post_meta($post_id, 'mpw-weight', true);
                if ($weight) {
                    $formatted_weight = number_format($weight / 10, 1, ',');
                    echo '<span class="weight-value">' . $formatted_weight . ' kg</span>';
                } else {
                    echo '<span style="color: #999;">—</span>';
                }
                break;

            case 'weight_change':
                $this->display_weight_change($post_id);
                break;
        }
    }

    /**
     * Display weight change compared to previous entry
     */
    private function display_weight_change($post_id) {
        $current_weight = get_post_meta($post_id, 'mpw-weight', true);
        $current_date = get_post_meta($post_id, 'mpw-date', true);
        
        if (!$current_weight || !$current_date) {
            echo '<span style="color: #999;">—</span>';
            return;
        }

        // Get previous weight entry
        $args = [
            'post_type' => 'mpw',
            'posts_per_page' => 1,
            'meta_key' => 'mpw-date',
            'orderby' => 'meta_value',
            'order' => 'DESC',
            'meta_type' => 'DATE',
            'meta_query' => [
                [
                    'key' => 'mpw-date',
                    'value' => $current_date,
                    'compare' => '<',
                    'type' => 'DATE'
                ]
            ],
            'post__not_in' => [$post_id]
        ];

        $previous_query = new \WP_Query($args);
        
        if ($previous_query->have_posts()) {
            $previous_query->the_post();
            $previous_weight = get_post_meta(get_the_ID(), 'mpw-weight', true);
            wp_reset_postdata();
            
            if ($previous_weight) {
                $diff = ($current_weight - $previous_weight) / 10;
                
                if ($diff > 0) {
                    echo '<span style="color: #d63638; font-weight: bold;">+' . number_format($diff, 1, ',') . ' kg ↗</span>';
                } elseif ($diff < 0) {
                    echo '<span style="color: #00a32a; font-weight: bold;">' . number_format($diff, 1, ',') . ' kg ↘</span>';
                } else {
                    echo '<span style="color: #787c82;">0 kg →</span>';
                }
            } else {
                echo '<span style="color: #999;">—</span>';
            }
        } else {
            echo '<span style="color: #999; font-style: italic;">First entry</span>';
        }
    }

    /**
     * Make custom columns sortable
     */
    public function make_columns_sortable($columns) {
        $columns['mpw_date'] = 'mpw_date';
        $columns['mpw_weight'] = 'mpw_weight';
        return $columns;
    }

    /**
     * Handle sorting for custom columns
     */
    public function handle_custom_column_sorting($vars) {
        if (isset($vars['orderby'])) {
            if ($vars['orderby'] == 'mpw_date') {
                $vars = array_merge($vars, [
                    'meta_key' => 'mpw-date',
                    'orderby' => 'meta_value',
                    'meta_type' => 'DATE'
                ]);
            } elseif ($vars['orderby'] == 'mpw_weight') {
                $vars = array_merge($vars, [
                    'meta_key' => 'mpw-weight',
                    'orderby' => 'meta_value_num'
                ]);
            }
        }
        return $vars;
    }

    /**
     * Add custom CSS for admin columns
     */
    public function admin_column_styles() {
        global $current_screen;

        if ($current_screen && $current_screen->post_type == 'mpw') {
            echo '<style>
                .column-mpw_date { width: 120px; }
                .column-mpw_weight { width: 100px; text-align: center; }
                .column-weight_change { width: 120px; text-align: center; }
                .weight-value {
                    background: #f0f8ff;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-weight: bold;
                }
                .fixed .column-mpw_date,
                .fixed .column-mpw_weight,
                .fixed .column-weight_change { text-align: center; }
            </style>';
        }
    }

    /**
     * Add weight goals submenu to the mpw post type menu
     */
    public function add_weight_goals_menu() {
        add_submenu_page(
            'edit.php?post_type=mpw',
            __('Weight Goals', 'mb-plugin'),
            __('cel', 'mb-plugin'),
            'manage_options',
            'weight-goals',
            [$this, 'weight_goals_page']
        );
    }

    /**
     * Display the weight goals admin page
     */
    public function weight_goals_page() {
        $current_goal = $this->get_current_weight_goal();
        $latest_weight = $this->get_latest_weight();

        ?>
        <div class="wrap">
            <h1><?php _e('Weight Goals', 'mb-plugin'); ?></h1>

            <?php if (isset($_GET['message'])): ?>
                <div class="notice notice-success is-dismissible">
                    <p>
                        <?php
                        if ($_GET['message'] == 'saved') {
                            _e('Goal saved successfully!', 'mb-plugin');
                        } elseif ($_GET['message'] == 'deleted') {
                            _e('Goal deleted successfully!', 'mb-plugin');
                        }
                        ?>
                    </p>
                </div>
            <?php endif; ?>

            <div class="card" style="max-width: 600px;">
                <h2><?php _e('Current Goal', 'mb-plugin'); ?></h2>

                <?php if ($current_goal): ?>
                    <div class="goal-info" style="margin-bottom: 20px; padding: 15px; background: #f9f9f9; border-radius: 5px;">
                        <p><strong><?php _e('Goal Weight:', 'mb-plugin'); ?></strong> <?php echo number_format($current_goal / 10, 1, ','); ?> kg</p>
                        <p><strong><?php _e('Set on:', 'mb-plugin'); ?></strong> <?php echo date('Y-m-d H:i', strtotime($this->get_goal_date())); ?></p>

                        <?php if ($latest_weight): ?>
                            <?php
                            $distance = ($latest_weight - $current_goal) / 10;
                            $distance_abs = abs($distance);
                            ?>
                            <p><strong><?php _e('Current Weight:', 'mb-plugin'); ?></strong> <?php echo number_format($latest_weight / 10, 1, ','); ?> kg</p>
                            <p><strong><?php _e('Distance to Goal:', 'mb-plugin'); ?></strong>
                                <span style="color: <?php echo $distance > 0 ? '#d63638' : '#00a32a'; ?>; font-weight: bold;">
                                    <?php echo $distance > 0 ? '+' : ''; ?><?php echo number_format($distance, 1, ','); ?> kg
                                    <?php echo $distance > 0 ? __('above goal', 'mb-plugin') : __('below goal', 'mb-plugin'); ?>
                                </span>
                            </p>
                        <?php endif; ?>

                        <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" style="margin-top: 15px;">
                            <input type="hidden" name="action" value="delete_weight_goal">
                            <?php wp_nonce_field('delete_weight_goal', 'weight_goal_nonce'); ?>
                            <input type="submit" class="button button-secondary" value="<?php _e('Delete Goal', 'mb-plugin'); ?>"
                                   onclick="return confirm('<?php _e('Are you sure you want to delete this goal?', 'mb-plugin'); ?>')">
                        </form>
                    </div>
                <?php else: ?>
                    <p><?php _e('No goal set yet.', 'mb-plugin'); ?></p>
                <?php endif; ?>

                <h3><?php echo $current_goal ? __('Update Goal', 'mb-plugin') : __('Set New Goal', 'mb-plugin'); ?></h3>

                <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                    <input type="hidden" name="action" value="save_weight_goal">
                    <?php wp_nonce_field('save_weight_goal', 'weight_goal_nonce'); ?>

                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="goal_weight"><?php _e('Goal Weight (kg)', 'mb-plugin'); ?></label>
                            </th>
                            <td>
                                <input type="number"
                                       id="goal_weight"
                                       name="goal_weight"
                                       step="0.1"
                                       min="30"
                                       max="200"
                                       value="<?php echo $current_goal ? number_format($current_goal / 10, 1, '.') : ''; ?>"
                                       required
                                       style="width: 100px;">
                                <p class="description"><?php _e('Enter your target weight in kilograms (e.g., 75.5)', 'mb-plugin'); ?></p>
                            </td>
                        </tr>
                    </table>

                    <?php submit_button($current_goal ? __('Update Goal', 'mb-plugin') : __('Set Goal', 'mb-plugin')); ?>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Handle saving weight goal
     */
    public function handle_save_weight_goal() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        if (!wp_verify_nonce($_POST['weight_goal_nonce'], 'save_weight_goal')) {
            wp_die(__('Security check failed.'));
        }

        $goal_weight = floatval($_POST['goal_weight']);

        if ($goal_weight < 30 || $goal_weight > 200) {
            wp_die(__('Invalid weight value. Please enter a weight between 30 and 200 kg.'));
        }

        // Convert to internal format (multiply by 10)
        $goal_weight_internal = intval($goal_weight * 10);

        // Save goal as WordPress option
        update_option('mb_weight_goal', $goal_weight_internal);
        update_option('mb_weight_goal_date', current_time('mysql'));

        wp_redirect(admin_url('edit.php?post_type=mpw&page=weight-goals&message=saved'));
        exit;
    }

    /**
     * Handle deleting weight goal
     */
    public function handle_delete_weight_goal() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        if (!wp_verify_nonce($_POST['weight_goal_nonce'], 'delete_weight_goal')) {
            wp_die(__('Security check failed.'));
        }

        delete_option('mb_weight_goal');
        delete_option('mb_weight_goal_date');

        wp_redirect(admin_url('edit.php?post_type=mpw&page=weight-goals&message=deleted'));
        exit;
    }

    /**
     * Get current weight goal
     */
    public function get_current_weight_goal() {
        return get_option('mb_weight_goal', false);
    }

    /**
     * Get goal date
     */
    public function get_goal_date() {
        return get_option('mb_weight_goal_date', false);
    }

    /**
     * Get latest weight measurement
     */
    public function get_latest_weight() {
        $args = [
            'post_type' => 'mpw',
            'posts_per_page' => 1,
            'order' => 'DESC',
            'meta_key' => 'mpw-date',
            'orderby' => 'meta_value',
            'meta_type' => 'DATE'
        ];

        $query = new \WP_Query($args);

        if ($query->have_posts()) {
            $query->the_post();
            $weight = get_post_meta(get_the_ID(), 'mpw-weight', true);
            wp_reset_postdata();
            return $weight ? intval($weight) : false;
        }

        wp_reset_postdata();
        return false;
    }
}
