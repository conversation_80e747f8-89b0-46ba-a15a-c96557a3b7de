<?php

namespace PluginMB\Modules\Weights;

class WeightsPaginationHTML {
    
    private $pagination_info;
    private $base_url;

    public function __construct($pagination_info, $base_url = '') {
        $this->pagination_info = $pagination_info;
        $this->base_url = $base_url ?: get_permalink();
    }

    public function generate_pagination() {
        $current = $this->pagination_info['current_page'];
        $total = $this->pagination_info['total_pages'];
        
        if ($total <= 1) {
            return '';
        }

        $html = '<div class="mb-pagination">';
        
        // Previous button
        if ($current > 1) {
            $prev_url = add_query_arg('weight_page', $current - 1, $this->base_url);
            $html .= '<a href="' . esc_url($prev_url) . '" class="mb-page-btn mb-prev">«</a>';
        }

        // Page numbers
        $html .= '<div class="mb-page-numbers">';
        
        $start = max(1, $current - 2);
        $end = min($total, $current + 2);
        
        if ($start > 1) {
            $url = add_query_arg('weight_page', 1, $this->base_url);
            $html .= '<a href="' . esc_url($url) . '" class="mb-page-num">1</a>';
            if ($start > 2) {
                $html .= '<span class="mb-page-dots">...</span>';
            }
        }
        
        for ($i = $start; $i <= $end; $i++) {
            if ($i == $current) {
                $html .= '<span class="mb-page-num mb-current">' . $i . '</span>';
            } else {
                $url = add_query_arg('weight_page', $i, $this->base_url);
                $html .= '<a href="' . esc_url($url) . '" class="mb-page-num">' . $i . '</a>';
            }
        }
        
        if ($end < $total) {
            if ($end < $total - 1) {
                $html .= '<span class="mb-page-dots">...</span>';
            }
            $url = add_query_arg('weight_page', $total, $this->base_url);
            $html .= '<a href="' . esc_url($url) . '" class="mb-page-num">' . $total . '</a>';
        }
        
        $html .= '</div>';

        // Next button
        if ($current < $total) {
            $next_url = add_query_arg('weight_page', $current + 1, $this->base_url);
            $html .= '<a href="' . esc_url($next_url) . '" class="mb-page-btn mb-next">»</a>';
        }

        $html .= '<div class="mb-pagination-info">';
        $html .= sprintf('strona %d z %d (%d pomiary(ów))', $current, $total, $this->pagination_info['total_posts']);
        $html .= '</div>';

        $html .= '</div>';

        return $html;
    }
}
