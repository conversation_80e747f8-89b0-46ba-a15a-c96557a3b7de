<?php
/*
Plugin Name: [MB] blog.mariusza
Description: Plugin wyświ<PERSON><PERSON><PERSON><PERSON><PERSON> zawart<PERSON> z customowych postów 'events', 'electro', 'weights' itd.
Version: 1.1.5
Author: <PERSON>z
*/

defined('ABSPATH') or die('No script kiddies please!');

use PluginMB\Modules\Electro\Electro;
use PluginMB\Modules\Weights\Weights;
use PluginMB\Modules\Events\Events;

// Autoloader for classeslkjhg f=-09876543wq

function mb_plugin_autoloader($class)
{
	$prefix   = 'PluginMB\\Modules\\';
	$base_dir = __DIR__ . '/modules/';
	$len      = strlen($prefix);
	if (strncmp($prefix, $class, $len) !== 0) {
		return;
	}
	$relative_class = substr($class, $len);
	$file           = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
	if (file_exists($file)) {
		require $file;
	}
}

spl_autoload_register('mb_plugin_autoloader');

//enqueue css style
function mb_plugin_register_css_styles()
{

	//css style for plugins modules
	wp_enqueue_style(
		'mb-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-plugin.css',
		[],
		"1.0.0"
	);

	// css style for a electro-module
	wp_register_style(
		'mb-electro-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-electro.css',
		[],
		"1.0.0"
	);

	// css style for a weights-module
	wp_register_style(
		'mb-weights-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-weights.css',
		[],
		"1.0.3"
	);

	// css style for a Events-Module
	wp_register_style(
		'mb-events-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-events.css',
		[],
		'1.0'
	);


	// css style for a Netatmo-Module
	wp_register_style(
		'mb-netatmo-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-netatmo.css',
		[],
		"1.0.1"
	);

	// JavaScript for Netatmo module
	wp_register_script(
		'mb-netatmo-script',
		plugin_dir_url(__FILE__) . 'assets/js/mb-netatmo.js',
		['jquery'],
		'1.0.1',
		true
	);
}



add_action('wp_enqueue_scripts', 'mb_plugin_register_css_styles');

// Instantiate modules if the classes exist
add_action('plugins_loaded', function () {
	new Electro();
	new Events();
	new Weights();
});

// Debug function to check file paths
function mb_debug_file_paths()
{
	if (current_user_can('administrator') && isset($_GET['debug_netatmo'])) {
		$css_path = plugin_dir_path(__FILE__) . 'assets/css/mb-netatmo.css';
		$js_path = plugin_dir_path(__FILE__) . 'assets/js/mb-netatmo.js';

		echo '<div style="background: #fff; border: 2px solid red; padding: 20px; margin: 20px;">';
		echo '<h2>Netatmo Debug Info</h2>';
		echo '<p>CSS File Path: ' . $css_path . ' - Exists: ' . (file_exists($css_path) ? 'Yes' : 'No') . '</p>';
		echo '<p>JS File Path: ' . $js_path . ' - Exists: ' . (file_exists($js_path) ? 'Yes' : 'No') . '</p>';
		echo '<p>Plugin URL: ' . plugin_dir_url(__FILE__) . '</p>';
		echo '<p>Plugin Path: ' . plugin_dir_path(__FILE__) . '</p>';
		echo '</div>';
	}
}
add_action('wp_footer', 'mb_debug_file_paths');
