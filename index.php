<?php
/**
 * Plugin Name: [MB] <PERSON><PERSON><PERSON><PERSON>
 * Description: rejest urodzin, im<PERSON><PERSON>, świat oraz innych uroczystości rodzinnych.
 * Version: 1.4.8
 * Author: <PERSON>z
 */

defined('ABSPATH') or die('No script kiddies please!');

// Include required classes
require_once plugin_dir_path(__FILE__) . 'classes/ReadJsonData.php';
require_once plugin_dir_path(__FILE__) . 'classes/DateConvert.php';
require_once plugin_dir_path(__FILE__) . 'classes/RenderHtml.php';
require_once plugin_dir_path(__FILE__) . 'classes/WeatherShortcode.php';
require_once plugin_dir_path(__FILE__) . 'classes/WeatherAdmin.php';
require_once plugin_dir_path(__FILE__) . 'classes/NetatmoShortcode.php';
require_once plugin_dir_path(__FILE__) . 'classes/NetatmoAdmin.php';
require_once plugin_dir_path(__FILE__) . 'classes/CalendarShortcode.php';
require_once plugin_dir_path(__FILE__) . 'classes/CalendarAllShortcode.php';
require_once plugin_dir_path(__FILE__) . 'classes/ShortcodeManager.php';

// Initialize admin classes if in admin area
if (is_admin()) {
    $netatmo_admin = new FamilyCalendar\NetatmoAdmin();
    $weather_admin = new FamilyCalendar\WeatherAdmin();
}

// Initialize the ShortcodeManager class
function initialize_family_calendar_shortcode()
{
	new \FamilyCalendar\ShortcodeManager();
}

add_action('init', 'initialize_family_calendar_shortcode');

function calendar_register_css_styles()
{
	wp_register_style(
		'mb-calendar-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-calendar.css',
		[],
		"1.1.2"
	);

	wp_register_style(
		'mb-calendar-js-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-calendar-js.css',
		[],
		"1.0.0"
	);

	wp_register_script(
		'mb-calendar-js',
		plugin_dir_url(__FILE__) . 'assets/js/mb-calendar.js',
		[],
		"1.0.0"
	);

	wp_register_style(
		'mb-calendar-wheather-style',
		plugin_dir_url(__FILE__) . 'assets/css/mb-calendar-weather.css',
		[],
		"1.0.0"
	);

}

add_action('wp_enqueue_scripts', 'calendar_register_css_styles');

// Register styles and scripts for Netatmo
function mb_netatmo_register_assets() {
    wp_register_style(
        'mb-netatmo-style',
        plugin_dir_url(__FILE__) . 'assets/css/mb-netatmo.css',
        [],
        time() // Use time() to force cache refresh during development
    );
    
    wp_register_script(
        'mb-netatmo-script',
        plugin_dir_url(__FILE__) . 'assets/js/mb-netatmo.js',
        ['jquery'],
        time(), // Use time() to force cache refresh during development
        true
    );
}
add_action('init', 'mb_netatmo_register_assets');
