<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e7c7c40e-8d6f-4ff8-bd82-d3a34722fe9c" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../mb-mpw-plugin/includes/CustomQuery.php" beforeDir="false" afterPath="$PROJECT_DIR$/../mb-mpw-plugin/includes/CustomQuery.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../mb-mpw-plugin/includes/WeightsShortcode.php" beforeDir="false" afterPath="$PROJECT_DIR$/../mb-mpw-plugin/includes/WeightsShortcode.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="DrupalConfiguration">
    <drupalPath />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../mb-mpw-plugin" />
  </component>
  <component name="JoomlaConfiguration">
    <joomlaConfigPath />
    <joomlaPath />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="/opt/homebrew/Cellar/php/8.3.3/bin/php" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2qR0Kn4Dvo6fGL80M3xBFOWozxp" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;settings.php.frameworks&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/modules" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-PS-242.23726.107" />
        <option value="bundled-php-predefined-a98d8de5180a-90914f2295cb-com.jetbrains.php.sharedIndexes-PS-242.23726.107" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e7c7c40e-8d6f-4ff8-bd82-d3a34722fe9c" name="Changes" comment="" />
      <created>1734610019153</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1734610019153</updated>
      <workItem from="1734610020350" duration="9917000" />
      <workItem from="1734684780069" duration="7339000" />
      <workItem from="1734801870684" duration="5283000" />
      <workItem from="1735852900213" duration="700000" />
      <workItem from="1736499208570" duration="23000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="WordPressConfiguration" enabled="true">
    <wordpressPath>$PROJECT_DIR$/../../..</wordpressPath>
  </component>
</project>