jQuery(document).ready(function($) {
    // Find all CO2 elements (both in main container and modules)
    $('.mb-netatmo-detail, .mb-netatmo-module-detail').each(function() {
        var $element = $(this);
        var $label = $element.find('.mb-netatmo-label, .mb-netatmo-module-label');
        
        // Check if this is a CO2 element
        if ($label.text().indexOf('CO2') !== -1) {
            var $valueElement = $element.find('.mb-netatmo-value, .mb-netatmo-module-value');
            if ($valueElement.length) {
                var co2Text = $valueElement.text();
                var co2Value = parseInt(co2Text, 10);
                
                if (co2Value < 800) {
                    $element.addClass('co2-good');
                } else if (co2Value < 1200) {
                    $element.addClass('co2-warning');
                } else {
                    $element.addClass('co2-danger');
                }
            }
        }
    });
});
