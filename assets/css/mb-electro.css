
.mb-electro-container {
    margin-bottom: 20px;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    padding: 12px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 6px 12px;
    border-radius: 8px;
    border: 1px solid #eee;
}


.mb-electro-container > div > p:first-child {
    font-weight: 600;
}
.mb-electro-container > div > p:last-child {
    font-size: .85em;
    color: var(--wp--preset--color--accent-2);
}

.mb-electro-content-flex {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px 10px;
}
.mb-electro-content-flex__item {
    display: flex;
    justify-content: space-between;
}

.mb-electro-content-flex__header {
    color: var(--wp--preset--color--accent-5);
    font-size: .85em;
    text-transform: uppercase;
    font-weight: 600;
    border-bottom: 1px solid #ddd;
}