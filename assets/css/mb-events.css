.wp-block-group {
    margin-top: 0;
}

hr {
    border: 0;
    height: 2px;
    background: var(--wp--preset--color--accent-3);
    margin-bottom: 20px;
    width: 100%;
}

.mbs-article {
    display: flex; /* Use flexbox for layout */
    align-items: flex-start; /* Align items at the start */
    margin-bottom: 1.5rem; /* Space between articles */
}

.mbs-article__left {
    margin-right: 1rem; /* Space between logo and content */
}

.mbs-logo img {
    width: 60px!important; /* Set a larger width for the logo */
    height: auto; /* Maintain aspect ratio */
}

.mbs-article__right {
    flex: 1; /* Take remaining space */
}

.mbs-month {
    font-size: 1.25rem;
    font-weight: bold;
}

.mbs-month-title,
.mbs-month-tag {
    font-size: .9rem;
}

.mbs-event__info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
}