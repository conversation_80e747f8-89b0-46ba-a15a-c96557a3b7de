hr {
  border: 0;
  height: 2px;
  background: var(--wp--preset--color--accent-1);
}

.mb-today-calendar,
.mb-month-calendar {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  text-align: center;
}

.mb-today-calendar-content {
  margin-bottom: 20px;
  padding: 12px;
  box-shadow: rgba(149, 157, 165, 0.2) 0 6px 12px;
  border-radius: 8px;
  border: 1px solid #eee;
}

.mb-month-calendar-title {
  font-size: 1.15rem;
  margin-bottom: 0;
  margin-top: 40px;
}

.mb-today-calendar > h3 {
  color: var(--wp--preset--color--accent-1);
}

.text-big {
  font-size: 1.25rem;
}

.mb-month-calendar > article {
  margin-bottom: 14px;
}

.mb-month-calendar > article > div:first-child {
  color: #666;
  text-transform: uppercase;
  font-size: .9rem;
}

.mb-month-calendar > article > div:last-child {
  font-size: .95rem;
}

.m-t-0 {
  margin-top: 0;
}
.w-50 {
  width: 50%;
}
.w-80 {
  width: 80%;
}

.mb-error {
  color: red;
}

.mb-info {
  color: blue;
}