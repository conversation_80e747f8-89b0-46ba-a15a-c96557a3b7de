* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Catamaran', sans-serif;
  background-color: #222;
}

.container {
  width: 100%;
  /*height: 100vh;*/
  background-position: center !important;
  background-size: cover !important;
  background-repeat: no-repeat;
}


/* section HEADER */
.header {
  width: 100%;
  height: 7vh;
  background-color: orangered;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin-bottom: 8px;
}

.header__title {
  color: white;
  text-transform: uppercase;
  text-shadow: 2px 2px 2px black;
  font-size: 1.5rem;
}

.header__arrow {
  padding-inline: 10px;
  font-size: 42px;
  cursor: pointer;
  user-select: none;
}

/* section MAIN (Calendar) */

.calendar {
  width: 100%;
  /*height: 90vh;*/
  padding: 5px;
  color: white;
  padding-top: 10px;
}

.calendar__days {
  position: absolute;
  bottom: 5vh;
  left: 5px;
  right: 5px;
  max-width: 800px;
  margin-inline: auto;
}


.week-days__name {
  padding: 5px 0;
  font-size: 18px;
  font-weight: 700;
  color: black;
  text-align: center;
  text-transform: uppercase;
  text-shadow: 2px 2px 2px gray;
}

.days {
  margin-inline: auto;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-gap: 3px;
}

.days__detail {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45px;
  font-size: 32px;
  font-weight: 700;
  background-color: rgb(50, 50, 50, .6);
  text-shadow: 2px 2px 2px black;
  border-radius: 5px;
  user-select: none;
  cursor: pointer;
}

.day-info {
  position: absolute;
  max-height: 45%;
  left: 5px;
  right: 5px;
  top: 8vh;
  padding: 20px 10px;
  background-color: rgb(50, 50, 50, .6);
  border: 2px solid orangered;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  text-shadow: 2px 2px 2px black;
  font-weight: 700;
}

.day-info__holiday {
  padding-inline: 30px;
  max-width: 100%;
  font-size: 24px;
  border-radius: 10px;
  text-shadow: 0 0 2px 1px black;
}

/* days detail styling */
.day-info__details {
  color: yellow;
}

.day-info__details>span {
  font-size: 18px;
}

.current-day {
  border: 3px solid blue;
}

.active-day {
  border: 3px solid orangered;
}

.celeb, .day-info__celeb {
  color: lightcoral;
  text-transform: uppercase;
}

.saturday {
  color: gray;
}

.sunday {
  color: red;
}

.holiday, .day-info__holiday {
  background-color: rgb(255, 215, 0, .8) !important;
  color: red;
}

.name-day, .day-info__nameDay {
  color: violet;
}

.birthday, .day-info__birthday {
  color: aqua;
}

.day-info__proverb {
  font-style: italic;
}

/* section FOOTER */
footer {
  width: 100%;
  height: 3vh;
  color: #ddd;
  text-align: center;
  line-height: 3vh;
}


.d-none {
  display: none;
}


@media (min-width: 810px) {

  .container {
    max-width: 810px;
    height: 100vh;
    margin-inline: auto;
  }

  .days__detail {
    height: 65px;
  }

  .day-info {
    max-width: 800px;
    margin-inline: auto;
    font-size: 130%;
  }

  .day-info__holiday {
    font-size: 34px;
  }

  .header__title {
    font-size: 2rem;
  }

  .header__arrow {
    transition: .3s;
  }

  .header__arrow:hover {
    scale: 1.5;
  }

}