.mb-netatmo-last-update {
    font-size: 1.05rem;
    font-style: italic;
    text-align: left;
    margin-bottom: 8px;
}

/* Main container for modules */
.mb-netatmo-modules {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40px;
    letter-spacing: 1px;
}

/* Temperature container */
.mb-netatmo-temp-container {
    flex: 1 1 calc(33.33% - 20px);
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 250px;
}

/* Temperature display */
.mb-netatmo-temp {
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
}

div.mb-netatmo-temp > div:last-child {
    display: flex;
    align-content: baseline;
    justify-content: center;
}

.mb-netatmo-title,
.mb-netatmo-temp,
.mb-netatmo-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.mb-netatmo-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0px;
    text-transform: uppercase;
}
.mb-netatmo-value-container {
    display: flex;
    align-items: start;
    justify-content: center;
    gap: 8px;
    color: var(--wp--preset--color--accent-1);
}

.mb-netatmo-value-container > span:last-child {
    color: var(--wp--preset--color--accent-1);
}

.mb-netatmo-value-main {
    font-size: 2.5rem;
    font-weight: bold;
}

.mb-netatmo-label {
    font-size: 0.85rem;
}
.mb-netatmo-unit {
    font-size: 0.5em;
    vertical-align: super;
    color: black;
    line-height: 36px;
}

.mb-netatmo-condition {
    display: block;
    margin-top: 5px;
    font-size: 0.75em;
    color: #555;
    text-transform: lowercase;
}

/* Min/Max temperature styling */
.mb-netatmo-temp-minmax {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    font-size: 0.9em;
    color: #666;
    border-top: 1px solid #eee;
}

.mb-netatmo-temp-min,
.mb-netatmo-temp-max {
    text-align: center;
}

.mb-netatmo-temp-min {
    color: blue;
}

.mb-netatmo-temp-max {
    color: red;
}

.mb-netatmo-minmax-label {
    font-weight: 600;
    margin-right: 5px;
}

.mb-netatmo-minmax-value {
    font-weight: bold;
    color: #333;
    font-size: 1.25em;
}

.mb-netatmo-minmax-time {
    color: #888;
    margin-left: 5px;
}

/* Details section */
.mb-netatmo-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
}

.mb-netatmo-detail {
    min-width: 220px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 6px;
}

/* CO2 indicator styling */
.mb-netatmo-detail.co2-good {
    background-color: #e6f4ea;
}

.mb-netatmo-detail.co2-warning {
    background-color: #fef7e0;
}

.mb-netatmo-detail.co2-danger {
    background-color: #fce8e6;
}

/* Responsive styling */
@media (min-width: 1024px) {
    .mb-netatmo-modules {
        gap: 12px;
        letter-spacing: 0.4px;
        margin-bottom: 60px;
    }

    .mb-netatmo-temp-container {
        flex: 1 1 calc(30% - 30px);
        align-content: center;
    }
}

@media (max-width: 767px) {
    .mb-netatmo-modules {
        flex-direction: column;
    }

    .mb-netatmo-temp-container {
        flex: 1 1 100%;
    }

    .mb-netatmo-details {
        grid-template-columns: 1fr;
        margin-top: 14px;
    }
}
