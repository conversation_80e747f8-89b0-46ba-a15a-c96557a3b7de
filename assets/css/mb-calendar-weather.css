/* Base styles */
.mb-weather-calendar {
  font-family: Arial, sans-serif;
  max-width: 100%;
  margin: 20px auto 0;
}

.mb-weather-calendar h3 {
  margin-top: 0;
  color: #333;
  text-align: center;
}

/* Location display */
.mb-weather-location {
  font-size: 1.3em;
  font-weight: bold;
  text-align: center;
  margin-bottom: 15px;
  color: #1565c0;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 5px;
}

/* Current weather section */
.mb-weather-current {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mb-weather-current-temp {
  font-size: 2.5em;
  font-weight: bold;
  margin-right: 15px;
  text-align: right;
}

.mb-weather-current-unit {
  font-size: 0.5em;
  vertical-align: super;
  color: black;
}

.mb-weather-current-icon {
  width: 64px;
  height: 64px;
}

.mb-weather-current-condition {
  text-align: center;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 10px;
}

/* Common panel styles */
.mb-weather-current-details,
.mb-weather-astro {
  background-color: rgba(255, 255, 255, 0.5);
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
}

/* Weather details section */
.mb-weather-current-details {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
}

.mb-weather-detail {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mb-weather-detail-label {
  color: #555;
}

.mb-weather-detail-value {
  color: #333;
  font-weight: bold;
}

/* Astronomical data section */
.mb-weather-astro {
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
}

.mb-weather-astro-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 5px 10px;
}

.mb-weather-astro-icon {
  width: 24px;
  height: 24px;
  margin-right: 5px;
}

.mb-weather-astro-label {
  font-size: 0.85em;
  color: #555;
  margin-bottom: 2px;
}

.mb-weather-astro-value {
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

/* Marine data section */
.mb-weather-marine {
  margin: 0 auto 20px;
  width: 60%;
  background-color: rgba(25, 118, 210, 0.1);
  padding: 10px;
  border-radius: 5px;
  text-align: center;
}

.mb-weather-marine-label {
  font-size: 0.9em;
  color: #1565c0;
  margin-bottom: 5px;
}

.mb-weather-marine-value {
  font-weight: bold;
  font-size: 1.2em;
  color: #0d47a1;
}

/* Tides section */
.mb-weather-tides {
  margin-bottom: 20px;
  background-color: rgba(25, 118, 210, 0.1);
  padding: 10px;
  border-radius: 5px;
}

.mb-weather-tides-title {
  font-size: 1em;
  color: #1565c0;
  margin-bottom: 10px;
  text-align: center;
  font-weight: bold;
}

.mb-weather-tides-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.mb-weather-tide-item {
  margin: 5px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  text-align: center;
  min-width: 100px;
}

.mb-weather-tide-type {
  font-weight: bold;
  color: #0d47a1;
  margin-bottom: 5px;
}

.mb-weather-tide-time {
  font-size: 0.9em;
  color: #333;
  margin-bottom: 3px;
}

.mb-weather-tide-height {
  font-size: 0.85em;
  color: #555;
}

/* Responsive styles */
@media (max-width: 768px) {
  .mb-weather-astro {
    justify-content: space-around;
  }

  .mb-weather-current-details {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .mb-weather-marine {
    width: 100%;
  }
}
