
.cars-container {
  margin-bottom: 20px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding: 12px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 6px 12px;
  border-radius: 8px;
  border: 1px solid #eee;
  padding: 10px 40px;
}

.cars-container h3 {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 1.25rem;
}

.cars-content-flex__name {
  margin-bottom: 10px;
  margin-top: 0;
  font-size: .9em;
  font-weight: 600;
  text-align: center;
}

.cars-content-flex {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 10px 10px;
}
.cars-content-flex__item {
  display: flex;
  justify-content: space-between;
}

.cars-content-flex__header {
  color: green;
  font-size: .85em;
  text-transform: uppercase;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.cars-container__content {
  padding-left: 25px;
  margin-top: 10px;
  margin-bottom: 20px;
}

.cars-container__content-text {
  display: flex;
  justify-content: space-between;
}

.cars-container__content-text {
  font-size: 0.95rem;
}

.cars-container__content-text > div:first-child {
  max-width: 220px;
}
.cars-container__content-text > div:last-child {
  font-weight: bold;
  width: 80px;
  text-align: right;
}

/* Add to your existing cars-shortcode-services.css */

.mb-current-car {
    text-align: center;
    margin-bottom: 20px;
}

.car-name {
    font-size: 1.5rem;
    margin: 10px 0;
    color: #333;
}

.car-details-button {
    background: #0073aa;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.car-details-button:hover {
    background: #005a87;
}

.car-details-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.car-details-hidden {
    display: none;
}

.car-details-visible {
    display: block;
    margin-top: 20px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #eee;
}

.loading {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: #666;
}

.error {
    text-align: center;
    padding: 20px;
    color: #d63031;
    background: #ffe6e6;
    border: 1px solid #d63031;
    border-radius: 4px;
}
