.mb-weights {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 6px 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  max-width: 360px;
  margin-bottom: 10px;
}

.mb-weights__date {
  width: 140px;
  font-size: 0.9rem;
  text-align: center;
}

.mb-weights__weight,
.mb-weights__diff {
  width: 80px;
  text-align: right;
}

.mb-weights__weight {
  background-color: #f0f0f0;
  color: var(--wp--preset--color--accent-1);
  padding: 4px;
  border-radius: 8px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
}

.mb-weights__icon {
  width: 50px;
  text-align: center;
  font-weight: 900;
}

/* text after summary block */
summary {
  text-align: center; 
}

/* Summary Styles */
.mb-weights-summary {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 8px auto;
  max-width: 500px;
}

.mb-summary-title {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.2em;
  text-align: center;
}

.mb-summary-period {
  margin: 0 0 15px 0;
  color: #666;
  font-style: italic;
  text-align: center;
}

.mb-summary-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.mb-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.mb-stat-label {
  font-size: 0.9em;
  color: #666;
  margin-bottom: 5px;
}

.mb-stat-value {
  font-size: 1.2em;
  font-weight: bold;
  color: #333;
}

.mb-stat-value.positive {
  color: #dc3545;
}

.mb-stat-value.negative {
  color: #28a745;
}

.mb-stat-value.neutral {
  color: #6c757d;
}

.mb-stat-date {
  font-size: 0.8em;
  color: #999;
  margin-top: 2px;
}

.mb-stat-full-row {
  grid-column: 1 / -1;
  text-align: center;
}

/* Month Navigation */
.mb-month-navigation {
  margin-bottom: 20px;
}

.mb-month-navigation h3 {
  margin-bottom: 15px;
}

.mb-month-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
}

.mb-month-link {
  padding: 5px 10px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  text-decoration: none;
  color: #495057;
  font-size: 0.9em;
}

.mb-month-link:hover {
  background: #e9ecef;
  text-decoration: none;
}

.mb-month-link.current {
  background: #007cba;
  color: white;
  border-color: #007cba;
}

/* Pagination Styles */
.mb-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.mb-page-btn,
.mb-page-num {
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  text-decoration: none;
  color: #495057;
  font-size: 0.9em;
}

.mb-page-btn:hover,
.mb-page-num:hover {
  background: #e9ecef;
  text-decoration: none;
}

.mb-page-num.mb-current {
  background: #007cba;
  color: white;
  border-color: #007cba;
}

.mb-page-dots {
  padding: 8px 4px;
  color: #6c757d;
}

.mb-pagination-info {
  width: 100%;
  text-align: center;
  margin-top: 10px;
  font-size: 0.9em;
  color: #6c757d;
}

/* List Container */
.mb-weights-list {
  max-width: 500px;
  margin: 12px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mb-weights-paginated {
  max-width: 500px;
}

.mb-weights-paginated h3 {
  margin: 0 0 10px 0;

  text-align: center;
}

/* Goal Styles */
.mb-weight-goal-status {
  margin-top: 15px;
}

.mb-weight-goal-status h4 {
  margin: 0 0 10px 0;
  color: #0073aa;
  font-size: 1.1em;
}

.mb-weight-goal-status .goal-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.mb-weight-goal-status .goal-info p {
  margin: 0;
  font-size: 0.95em;
}

.goal-achieved {
  color: #28a745 !important;
  font-weight: bold;
}

.goal-distance.above-goal .distance-value {
  color: #dc3545;
  font-weight: bold;
}

.goal-distance.below-goal .distance-value {
  color: #28a745;
  font-weight: bold;
}

.goal-distance .distance-text {
  color: #666;
  font-size: 0.9em;
}

.goal-status {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8em;
  font-weight: bold;
  margin-left: 8px;
}

.goal-status.at-goal {
  background: #d4edda;
  color: #155724;
}

.goal-status.above-goal {
  background: #f8d7da;
  color: #721c24;
}

.goal-status.below-goal {
  background: #d1ecf1;
  color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {

  .mb-month-selector {
    justify-content: center;
  }

  .mb-pagination {
    font-size: 0.8em;
  }

  .mb-page-btn,
  .mb-page-num {
    padding: 6px 8px;
  }

  .mb-weight-goal-status .goal-info {
    font-size: 0.9em;
  }
}
