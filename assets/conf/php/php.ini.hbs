[PHP]
engine = On
{{#if os.windows}}
extension_dir="{{extensionsDir}}"
sys_temp_dir="C:\Windows\TEMP"
{{/if}}

; Maxes
max_execution_time = 1200
max_input_time = 600
max_input_vars = 4000
memory_limit = 256M
post_max_size = 1000M
max_file_uploads = 20
output_buffering = 4096

; Error Handling
error_reporting = E_ALL & ~E_DEPRECATED
error_log = "{{logs.errorLog}}"
log_errors = On
{{!-- log_errors_max_len = 1024 --}}
display_errors = On
display_startup_errors = On
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
{{!-- track_errors = Off --}}
html_errors = On

; Sendmail
sendmail_path = "\"{{mail.mailhogPath}}\" sendmail --smtp-addr={{mail.mailhogSmtpAddr}} <EMAIL>"

; Other
short_open_tag = On
asp_tags = Off
precision = 14
y2k_compliance = On
zlib.output_compression = Off
implicit_flush = Off
unserialize_callback_func =
serialize_precision = 17
allow_call_time_pass_reference = Off
safe_mode = Off
safe_mode_gid = Off
safe_mode_include_dir =
safe_mode_exec_dir =
safe_mode_allowed_env_vars = PHP_
safe_mode_protected_env_vars = LD_LIBRARY_PATH
disable_functions = {{! @todo Look into re-enabling this. This was initially disabled due to a segfault on PHP 7.3. }}
disable_classes =
zend.enable_gc = On
expose_php = On
variables_order = "GPCS"
request_order = "GP"
register_globals = Off
register_long_arrays = Off
register_argc_argv = Off
auto_globals_jit = On
magic_quotes_gpc = Off
magic_quotes_runtime = Off
magic_quotes_sybase = Off
auto_prepend_file =
auto_append_file =
default_mimetype = "text/html"
default_charset = "UTF-8"
include_path = ".:/usr/share/php:/www/wp-content/pear"
doc_root =
user_dir =
enable_dl = Off
cgi.fix_pathinfo=1
file_uploads = On
upload_max_filesize = 300M
allow_url_fopen = On
allow_url_include = Off
default_socket_timeout = 60
openssl.cafile="{{wpCaBundlePath}}"

{{#if os.windows}}
; Load extensions
extension=php_curl.dll
extension=php_gettext.dll
extension=php_openssl.dll
extension=php_exif.dll
extension=php_gd.dll
extension=php_ftp.dll
extension=php_ffi.dll
extension=php_imap.dll
extension=php_bz2.dll
extension=php_mbstring.dll
extension=php_sodium.dll
extension=php_tidy.dll
extension=php_xsl.dll
extension=php_soap.dll
extension=php_mysqli.dll
extension=php_pdo_mysql.dll
extension=php_fileinfo.dll
extension=php_pdo_sqlite.dll
extension=php_sqlite3.dll
extension=php_zip.dll
{{/if}}

[MySQL]
mysql.allow_local_infile = On
mysql.allow_persistent = On
mysql.cache_size = 2000
mysql.max_persistent = -1
mysql.max_links = -1
mysql.default_port = {{mysql.port}}
mysql.default_socket = "{{mysql.socket}}"
mysql.default_host =
mysql.default_user =
mysql.default_password =
mysql.connect_timeout = 60
mysql.trace_mode = Off

[MySQLi]
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.cache_size = 2000
mysqli.default_port = {{mysql.port}}
mysqli.default_socket = "{{mysql.socket}}"
mysqli.default_host =
mysqli.default_user =
mysqli.default_pw =
mysqli.reconnect = Off

[Pdo_Mysql]
pdo_mysql.default_socket = "{{mysql.socket}}"
pdo_mysql.default_port = {{mysql.port}}

[imagick]
{{#if os.windows}}
extension = php_imagick.dll
{{else}}
extension = {{extensionsDir}}/imagick.so
{{/if}}

[Date]
[filter]
[iconv]
[intl]
{{#if os.windows}}
extension = php_intl.dll
{{/if}}

[sqlite]
[sqlite3]
[Pcre]

[Phar]
[Syslog]
define_syslog_variables  = Off

[mail function]
SMTP = localhost
smtp_port = 25
mail.add_x_header = On

[SQL]
sql.safe_mode = Off

[OCI8]

[bcmath]
bcmath.scale = 0

[browscap]

[Session]
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /
session.cookie_domain =
session.cookie_httponly =
session.serialize_handler = php
session.gc_probability = 0
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.bug_compat_42 = Off
session.bug_compat_warn = Off
session.referer_check =
session.entropy_length = 0
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0
session.hash_function = 0
session.hash_bits_per_character = 5
url_rewriter.tags = "a=href,area=href,frame=src,input=src,form=fakeentry"

[Assertion]
[COM]
[mbstring]
[gd]
[exif]
[Tidy]
tidy.clean_output = Off

[soap]
soap.wsdl_cache_enabled=1
soap.wsdl_cache_ttl=86400
soap.wsdl_cache_limit = 5

[sysvshm]

[ldap]
ldap.max_links = -1

[mcrypt]
[dba]

{{#unless apache}}
[opcache]
{{#if os.windows}}
zend_extension = php_opcache.dll
{{else}}
zend_extension = {{extensionsDir}}/opcache.so
{{/if}}

opcache.enable=1
opcache.enable_cli=1
{{/unless}}

[xdebug]
{{#if os.windows}}
zend_extension = php_xdebug.dll
{{else}}
zend_extension = {{extensionsDir}}/xdebug.so
{{/if}}

{{#if xdebugEnabled}}
xdebug.mode=debug,develop
xdebug.client_port=9003
xdebug.start_with_request=yes
xdebug.discover_client_host=yes
xdebug.log_level=0
{{else}}
xdebug.mode=off
{{/if}}

[Pcre]
pcre.jit=0 {{! Disable due to limitation with macOS Notarization }}
