{{! This file will not be used on Windows due to PHP-FPM not being available on Windows. }}
[www]
listen = "{{phpFpm.socket}}"
pm = static
pm.max_children = 2
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3
catch_workers_output = yes

{{! Environment Variables for imagick }}
env[MAGICK_CODER_MODULE_PATH] = "{{imageMagick.codersDir}}"
env[GS_LIB] = "{{imageMagick.ghostscriptLib}}"
env[PATH] = "{{imageMagick.ghostscriptBin}}"
